import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const templateId = formData.get('templateId') as string;
    const photo = formData.get('photo') as File;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (!photo) {
      return NextResponse.json(
        { error: 'Photo file is required' },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!validTypes.includes(photo.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and GIF are supported' },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (photo.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 5MB' },
        { status: 400 }
      );
    }

    // Create the template folder path in public directory
    const templateFolderPath = path.join(process.cwd(), 'public', templateId);
    
    // Ensure the folder exists
    try {
      await fs.mkdir(templateFolderPath, { recursive: true });
    } catch (error) {
      console.error('Error creating template folder:', error);
      return NextResponse.json(
        { error: 'Failed to create template folder' },
        { status: 500 }
      );
    }

    // Generate a unique filename for the photo
    const fileExtension = path.extname(photo.name);
    const photoFilename = `applicant_photo${fileExtension}`;
    const photoPath = path.join(templateFolderPath, photoFilename);

    try {
      // Convert file to buffer and save
      const arrayBuffer = await photo.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      await fs.writeFile(photoPath, buffer);

      // Return success response with the photo path
      return NextResponse.json({
        success: true,
        photoPath: `/${templateId}/${photoFilename}`,
        message: 'Photo uploaded successfully'
      });
    } catch (error) {
      console.error('Error saving photo:', error);
      return NextResponse.json(
        { error: 'Failed to save photo' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing photo upload:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
