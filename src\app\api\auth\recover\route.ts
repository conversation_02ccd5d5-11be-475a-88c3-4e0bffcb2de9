import { NextRequest, NextResponse } from 'next/server';
import { recoverAccount, UserModel } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Initialize database if not already done
    await initDatabase();
    
    const body = await request.json();
    const { username, recoveryMethod, privateKey, securityAnswers } = body;

    if (!username || !recoveryMethod) {
      return NextResponse.json(
        { success: false, message: 'Username and recovery method are required' },
        { status: 400 }
      );
    }

    if (recoveryMethod === 'privateKey' && !privateKey) {
      return NextResponse.json(
        { success: false, message: 'Private key is required for private key recovery' },
        { status: 400 }
      );
    }

    if (recoveryMethod === 'securityQuestions' && (!securityAnswers || securityAnswers.length === 0)) {
      return NextResponse.json(
        { success: false, message: 'Security answers are required for security questions recovery' },
        { status: 400 }
      );
    }

    const result = await recoverAccount({
      username,
      recoveryMethod,
      privateKey,
      securityAnswers
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        newPassword: result.newPassword
      });
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Recovery API error:', error);
    return NextResponse.json(
      { success: false, message: 'Account recovery failed' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve security questions for a user
export async function GET(request: NextRequest) {
  try {
    await initDatabase();
    
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json(
        { success: false, message: 'Username is required' },
        { status: 400 }
      );
    }

    const user = await UserModel.findByUsername(username);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    if (user.recoveryOptions.securityQuestions) {
      const questions = user.recoveryOptions.securityQuestions.map(sq => sq.question);
      return NextResponse.json({
        success: true,
        questions,
        hasPrivateKey: !!user.recoveryOptions.privateKey
      });
    } else {
      return NextResponse.json({
        success: true,
        questions: [],
        hasPrivateKey: !!user.recoveryOptions.privateKey
      });
    }
  } catch (error) {
    console.error('Get recovery info API error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get recovery information' },
      { status: 500 }
    );
  }
}
