"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import {
  useNotificationsQuery,
  useCreateNotificationMutation,
  useMarkAsReadMutation,
  useMarkAllAsReadMutation,
  useDeleteNotificationMutation,
  useClearAllNotificationsMutation,
  type Notification as NotificationHook,
  type CreateNotificationData,
} from "@/hooks/useNotifications";

// Re-export types for backward compatibility
export type { PDFData } from "@/hooks/useNotifications";
export type Notification = NotificationHook;

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isAdminMode: boolean;
  isLoading: boolean;
  addNotification: (notification: CreateNotificationData) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  removeNotificationLocally: (id: string) => void;
  clearAllNotifications: () => void;
  refreshNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isAdminMode, setIsAdminMode] = useState(false);

  // Use React Query hooks for notifications - always enabled to receive real-time updates
  const {
    data: allNotifications = [],
    isLoading,
    refetch: refreshNotifications,
  } = useNotificationsQuery(true); // Always enabled

  // Only show notifications when admin mode is enabled
  const notifications = isAdminMode ? allNotifications : [];

  // Mutation hooks
  const createNotificationMutation = useCreateNotificationMutation();
  const markAsReadMutation = useMarkAsReadMutation();
  const markAllAsReadMutation = useMarkAllAsReadMutation();
  const deleteNotificationMutation = useDeleteNotificationMutation();
  const clearAllNotificationsMutation = useClearAllNotificationsMutation();

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  // Check admin mode and load notifications
  useEffect(() => {
    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setIsAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, []);

  // Wrapper functions for mutations
  const addNotification = (notificationData: CreateNotificationData) => {
    createNotificationMutation.mutate(notificationData);
  };

  const markAsRead = (id: string) => {
    markAsReadMutation.mutate(id);
  };

  const markAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  const removeNotification = (id: string) => {
    deleteNotificationMutation.mutate(id);
  };

  const removeNotificationLocally = (id: string) => {
    // Remove notification from local state only (for when it's already deleted on server)
    // This is used when the notification has already been deleted on the server (e.g., during approval)
    // and we just need to update the local cache without making another API call
    refreshNotifications();
  };

  const clearAllNotifications = () => {
    clearAllNotificationsMutation.mutate();
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isAdminMode,
    isLoading,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    removeNotificationLocally,
    clearAllNotifications,
    refreshNotifications: () => refreshNotifications(),
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
}
