import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId } = body;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Construct the photo path
    const templateFolderPath = path.join(process.cwd(), 'public', templateId);
    
    // Look for applicant photo files with common extensions
    const photoExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
    const deletedFiles = [];

    for (const ext of photoExtensions) {
      const photoPath = path.join(templateFolderPath, `applicant_photo${ext}`);

      try {
        await fs.access(photoPath);
        await fs.unlink(photoPath);
        deletedFiles.push(`applicant_photo${ext}`);
      } catch {
        // File doesn't exist, continue to next extension
        continue;
      }
    }

    return NextResponse.json({
      success: true,
      message: deletedFiles.length > 0 
        ? `Deleted ${deletedFiles.length} photo(s): ${deletedFiles.join(', ')}`
        : 'No photos found to delete',
      deletedFiles
    });
  } catch (error) {
    console.error('Error deleting photo:', error);
    return NextResponse.json(
      { error: 'Failed to delete photo' },
      { status: 500 }
    );
  }
}
