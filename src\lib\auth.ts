// Re-export everything from the new user model for backward compatibility
export {
  type User,
  type LoginCredentials,
  type SignupData,
  type RecoveryData,
  SECURITY_QUESTIONS,
  encrypt,
  decrypt,
  hashPassword,
  generatePrivateKey,
  signup,
  login,
  recoverAccount,
  UserModel
} from './models/user';

// Legacy functions for backward compatibility with existing JSON-based storage
// Server-side only imports
let fs: any;
let path: any;

if (typeof window === 'undefined') {
  fs = require('fs').promises;
  path = require('path');
}

import { encrypt, decrypt } from './models/user';
import type { User } from './models/user';

// File paths for legacy support
const USERS_DIR = typeof window === 'undefined' ? path?.join(process.cwd(), 'data') : '';
const USERS_FILE_PATH = typeof window === 'undefined' ? path?.join(USERS_DIR, 'users.json') : '';

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  try {
    await fs.access(USERS_DIR);
  } catch {
    await fs.mkdir(USERS_DIR, { recursive: true });
  }
};

// Legacy storage utilities (kept for migration purposes)
export const getStoredUsers = async (): Promise<User[]> => {
  try {
    await ensureDataDir();
    const encryptedData = await fs.readFile(USERS_FILE_PATH, 'utf-8');
    const decryptedData = decrypt(encryptedData);
    return JSON.parse(decryptedData);
  } catch (error) {
    // File doesn't exist or is empty, return empty array
    if ((error as any).code === 'ENOENT') {
      return [];
    }
    console.error('Error loading users:', error);
    return [];
  }
};

export const saveUsers = async (users: User[]): Promise<void> => {
  try {
    await ensureDataDir();
    const encryptedData = encrypt(JSON.stringify(users, null, 2));
    await fs.writeFile(USERS_FILE_PATH, encryptedData, 'utf-8');
  } catch (error) {
    console.error('Error saving users:', error);
    throw error;
  }
};
