import { NextRequest, NextResponse } from 'next/server';
// import { verifyDocumentOnline } from '@/lib/supabase'; // Disabled - scan tracking removed

// GET /api/validate-online/[id] - Verify document online via Supabase
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Document ID is required' },
        { status: 400 }
      );
    }

    // Online validation temporarily disabled - scan tracking removed
    return NextResponse.json({
      success: false,
      isValid: false,
      error: 'Online validation temporarily unavailable',
      message: 'Please use local validation instead at /validate/' + id
    }, { status: 503 });
  } catch (error) {
    console.error('Error validating document online:', error);
    return NextResponse.json(
      { 
        success: false, 
        isValid: false,
        error: 'Failed to validate document online',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
