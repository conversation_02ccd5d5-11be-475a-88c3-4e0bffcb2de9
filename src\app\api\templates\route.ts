import { NextRequest, NextResponse } from 'next/server';
import {
  loadTemplates,
  addTemplate,
  cleanWordHtml,
  saveTemplateFile
} from '@/lib/templates';
import {
  extractPlaceholders,
  generateTemplateFilename,
  updateImagePaths,
  hasApplicantPhoto
} from '@/lib/template-utils';
import { getUserFromSession } from '@/lib/auth-utils';
import { TemplateModel } from '@/lib/models/template';

export async function GET(request: NextRequest) {
  try {
    // Get user from session for filtering
    const user = await getUserFromSession(request);

    // Use the new model method with user filtering
    const templates = await TemplateModel.findAll(user?.id);

    // Debug logging
    console.log('Templates API called');
    console.log('User from session:', user);
    console.log('Templates found:', templates.length);
    console.log('Templates:', templates);

    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error loading templates:', error);
    console.error('Error details:', error);
    return NextResponse.json(
      { error: 'Failed to load templates', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user from session for ownership
    const user = await getUserFromSession(request);

    const body = await request.json();
    const { name, description, layoutSize, htmlContent, hasFolder } = body;

    // Validate input
    if (!name || !description || !htmlContent || !layoutSize) {
      return NextResponse.json(
        { error: 'Name, description, layout size, and HTML content are required' },
        { status: 400 }
      );
    }

    // Validate layout size
    if (layoutSize !== 'A4' && layoutSize !== 'Letter') {
      return NextResponse.json(
        { error: 'Layout size must be either A4 or Letter' },
        { status: 400 }
      );
    }

    // Generate unique filename and ID first
    const filename = generateTemplateFilename(name);
    const id = filename.replace('.html', '');

    // Clean the HTML content and update image paths if folder is uploaded
    let cleanedHtml = cleanWordHtml(htmlContent);

    if (hasFolder) {
      // Update image src paths to point to the uploaded folder
      cleanedHtml = updateImagePaths(cleanedHtml, id);
    }



    // Extract placeholders
    const placeholders = extractPlaceholders(cleanedHtml);

    // Check if template has applicant photo
    const hasApplicantPhotoFlag = hasApplicantPhoto(cleanedHtml);

    // Save the HTML file
    await saveTemplateFile(filename, cleanedHtml);

    // Add template to database with user ownership
    await TemplateModel.create({
      id,
      name: name.trim(),
      description: description.trim(),
      filename,
      placeholders,
      layoutSize,
      hasApplicantPhoto: hasApplicantPhotoFlag,
    }, user?.id);

    return NextResponse.json({
      success: true,
      template: {
        id,
        name: name.trim(),
        description: description.trim(),
        filename,
        placeholders,
        layoutSize,
      }
    });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}
