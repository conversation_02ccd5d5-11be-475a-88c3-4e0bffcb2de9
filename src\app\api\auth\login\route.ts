import { NextRequest, NextResponse } from 'next/server';
import { login } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Initialize database if not already done
    await initDatabase();
    
    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      return NextResponse.json(
        { success: false, message: 'Username and password are required' },
        { status: 400 }
      );
    }

    const result = await login({ username, password });

    if (result.success) {
      // Get user details to include user ID in session
      const { UserModel } = await import('@/lib/models/user');
      const user = await UserModel.findByUsername(username);

      if (!user) {
        return NextResponse.json(
          { success: false, message: 'User not found' },
          { status: 401 }
        );
      }

      // Create a simple session token with user ID (in production, use proper JWT)
      const sessionData = {
        username: user.username,
        userId: user.id,
        timestamp: Date.now()
      };
      const sessionToken = Buffer.from(JSON.stringify(sessionData)).toString('base64');

      const response = NextResponse.json({
        success: true,
        message: result.message,
        user: { username: user.username, id: user.id }
      });

      // Set session cookie
      response.cookies.set('admin-session', sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      return response;
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json(
      { success: false, message: 'Login failed' },
      { status: 500 }
    );
  }
}
