"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function AdminHomePage() {
  const router = useRouter();
  const [adminMode, setAdminMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check admin mode on component mount
  useEffect(() => {
    const checkAdminMode = () => {
      const savedAdminMode = localStorage.getItem("adminMode") === "true";
      setAdminMode(savedAdminMode);
      setIsLoading(false);

      if (!savedAdminMode) {
        toast.error("Admin access required");
        router.push("/settings");
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    window.addEventListener("adminModeChanged", handleAdminModeChange);
    return () => {
      window.removeEventListener("adminModeChanged", handleAdminModeChange);
    };
  }, [router]);

  // Show loading state while checking admin mode
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Checking permissions...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!adminMode) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to access the admin dashboard.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center py-12">
        <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
        <h1 className="text-3xl font-semibold mb-2">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to the admin dashboard. Admin features are available
          throughout the application.
        </p>
      </div>
    </div>
  );
}
