"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { QrCode, Shield } from "lucide-react";
import { QRCodeDialog } from "@/components/qr-code-dialog";
import { toast } from "sonner";

export default function QRCodePage() {
  const router = useRouter();
  const [adminMode, setAdminMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showQRDialog, setShowQRDialog] = useState(false);

  // Check admin mode on component mount
  useEffect(() => {
    const checkAdminMode = () => {
      const savedAdminMode = localStorage.getItem("adminMode") === "true";
      setAdminMode(savedAdminMode);
      setIsLoading(false);

      if (!savedAdminMode) {
        toast.error("Admin access required");
        router.push("/settings");
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    window.addEventListener("adminModeChanged", handleAdminModeChange);
    return () => {
      window.removeEventListener("adminModeChanged", handleAdminModeChange);
    };
  }, [router]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!adminMode) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to access QR code generation.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="flex items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-semibold text-foreground">
            QR Code Generator
          </h1>
          <p className="text-muted-foreground">
            Generate QR codes for mobile access to the upload page.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* QR Code Generation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              QR Code for Mobile Access
            </CardTitle>
            <CardDescription>
              Generate a QR code that allows mobile devices to automatically
              connect to your hotspot and access the upload page. This creates a
              seamless experience for users to upload documents from their
              mobile devices.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setShowQRDialog(true)}
              className="w-full justify-start"
              variant="outline"
            >
              <QrCode className="h-4 w-4 mr-2" />
              Generate QR Code
            </Button>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">1. Generate QR Code</h4>
              <p className="text-sm text-muted-foreground">
                Click the &quot;Generate QR Code&quot; button above to create a
                QR code with your current network settings.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">2. Share with Users</h4>
              <p className="text-sm text-muted-foreground">
                Display the QR code for users to scan with their mobile devices.
                The QR code will automatically connect them to your hotspot and
                redirect to the upload page.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">3. Monitor Uploads</h4>
              <p className="text-sm text-muted-foreground">
                Users can upload documents directly from their mobile devices,
                and you&apos;ll receive notifications for each upload.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* QR Code Dialog */}
      <QRCodeDialog open={showQRDialog} onOpenChange={setShowQRDialog} />
    </div>
  );
}
