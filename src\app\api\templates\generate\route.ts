import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, loadTemplateFile, replacePlaceholders, generatePDFStyles } from '@/lib/templates';
import { replaceApplicantPhoto } from '@/lib/template-utils';
import { createDocumentQRCode } from '@/lib/database';

// import { syncQRCodeToSupabase } from '@/lib/supabase'; // Disabled - scan tracking removed

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, data, photoPath, photoBase64, addQRCode, qrCodeText, documentId, documentType } = body;

    // Validate required fields
    if (!templateId || !data) {
      return NextResponse.json(
        { error: 'Template ID and data are required' },
        { status: 400 }
      );
    }

    // Load and validate template
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Process template content
    const templateContent = await loadTemplateFile(template.filename);
    let processedContent = replacePlaceholders(templateContent, data);

    // Handle photo replacement
    if (photoPath) {
      processedContent = replaceApplicantPhoto(processedContent, photoPath);
      console.log('Photo replaced using photoPath:', photoPath);
    } else if (photoBase64) {
      console.log('Replacing photo with base64 data, length:', photoBase64.length);

      // First, replace the applicant photo with a recognizable placeholder while preserving dimensions
      processedContent = processedContent.replace(
        /<img([^>]*?)alt\s*=\s*["'][^"']*applicants_photo[^"']*["']([^>]*?)>/gi,
        '<img$1alt="applicants_photo"$2 src="APPLICANT_PHOTO_PLACEHOLDER" style="object-fit: cover; object-position: center;">'
      );

      // Also try to replace specific image files that are likely photo placeholders
      processedContent = processedContent.replace(
        /<img([^>]*?)src="[^"]*image003\.png"([^>]*?)>/gi,
        '<img$1src="APPLICANT_PHOTO_PLACEHOLDER"$2 style="object-fit: cover; object-position: center;">'
      );

      // Now replace the placeholder with the base64 image data
      const originalContent = processedContent;
      processedContent = processedContent.replace(
        /src="APPLICANT_PHOTO_PLACEHOLDER"/gi,
        `src="${photoBase64}"`
      );

      // Also try generic patterns as fallback
      processedContent = processedContent.replace(
        /src="[^"]*placeholder[^"]*"/gi,
        `src="${photoBase64}"`
      );
      processedContent = processedContent.replace(
        /src="[^"]*photo[^"]*"/gi,
        `src="${photoBase64}"`
      );

      if (originalContent === processedContent) {
        console.warn('No photo placeholders found to replace. Template content preview:',
          processedContent.substring(0, 500));
        // Log what images are in the template for debugging
        const imgMatches = processedContent.match(/<img[^>]*>/gi);
        console.log('Available images in template:', imgMatches);
      } else {
        console.log('Photo replacement successful');
      }
    } else {
      console.log('No photo data provided (photoPath or photoBase64)');
    }

    // Add QR code if requested
    if (addQRCode && qrCodeText) {
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrCodeText)}`;

      const qrCodeHtml = `
        <div style="position: absolute; bottom: 15px; left: 15px; z-index: 1000;">
          <div style="background: white; padding: 8px; border: 1px solid #ddd; border-radius: 6px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.15); width: 76px; box-sizing: border-box;">
            <img src="${qrCodeUrl}" alt="QR Code" style="width: 60px; height: 60px; margin: 0 auto 4px auto; display: block;" />
            <div style="font-size: 10px; color: #555; font-family: Arial, sans-serif; font-weight: 500; line-height: 1.2;">Scan to Validate</div>
          </div>
        </div>
      `;

      processedContent = processedContent.replace('</body>', `${qrCodeHtml}</body>`);

      // Store QR code in database if document ID and type are provided
      if (documentId && documentType) {
        try {
          const qrCodeData = {
            document_id: documentId,
            qr_code_data: qrCodeUrl,
            validation_url: qrCodeText
          };

          // Store in local database
          const localQRCode = await createDocumentQRCode(qrCodeData);
          console.log(`QR code stored locally for ${documentType} ${documentId}`);

          // Supabase sync disabled - scan tracking removed
          // try {
          //   await syncQRCodeToSupabase(localQRCode);
          //   console.log(`QR code synced to Supabase for ${documentType} ${documentId}`);
          // } catch (supabaseError) {
          //   console.error('Failed to sync QR code to Supabase:', supabaseError);
          //   // Don't fail if Supabase sync fails - local storage is primary
          // }
        } catch (error) {
          console.error('Failed to store QR code in database:', error);
          // Don't fail the entire request if QR code storage fails
        }
      }
    }

    // Apply PDF styles
    const pdfStyles = generatePDFStyles(template.layoutSize);
    const htmlWithStyles = processedContent.replace('</head>', `${pdfStyles}</head>`);

    return NextResponse.json({
      success: true,
      htmlContent: htmlWithStyles,
      templateName: template.name,
      layoutSize: template.layoutSize
    });
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}