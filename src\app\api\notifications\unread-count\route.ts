import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';

// GET /api/notifications/unread-count - Get unread notification count
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    const unreadCount = await NotificationModel.getUnreadCount(
      userId ? parseInt(userId) : undefined
    );
    
    return NextResponse.json({
      success: true,
      unreadCount
    });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch unread count' },
      { status: 500 }
    );
  }
}
