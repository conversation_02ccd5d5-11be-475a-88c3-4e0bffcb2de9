import { getDatabase } from '../database';

// Generate UUID function for server-side use
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
  status?: string | null; // null = pending, "approved" = approved, etc.
  approvedAt?: string;
  approvedBy?: string;
  originalFileName?: string;
  uploadedAt?: string;
  fileSize?: number;
  extractedAt?: string;
  updatedAt?: string;
}

export interface DatabaseDocument {
  id: string;
  templateName: string;
  applicantsName: string;
  uploadedAt: string;
  pdfFileName?: string;
  pdfData?: PDFData;
  userId?: number;
  status: "pending" | "approved" | "rejected";
  approvedAt?: string;
}

export interface CreateDocumentData {
  templateName: string;
  applicantsName: string;
  pdfFileName?: string;
  pdfData?: PDFData;
  userId?: number;
  status?: "pending" | "approved" | "rejected";
}

// Document database operations
export class DocumentModel {
  static async create(documentData: CreateDocumentData): Promise<DatabaseDocument> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const id = generateUUID();
    
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO documents (
        id, template_name, applicants_name, pdf_filename, pdf_data, user_id, status, uploaded_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      documentData.templateName,
      documentData.applicantsName,
      documentData.pdfFileName || null,
      documentData.pdfData ? JSON.stringify(documentData.pdfData) : null,
      documentData.userId || null,
      documentData.status || 'pending',
      now
    );
    
    return await this.findById(id) as DatabaseDocument;
  }

  static async findById(id: string, userId?: number): Promise<DatabaseDocument | null> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let row;

    if (userId) {
      stmt = db.prepare('SELECT * FROM documents WHERE id = ? AND (user_id = ? OR user_id IS NULL)');
      row = stmt.get(id, userId);
    } else {
      stmt = db.prepare('SELECT * FROM documents WHERE id = ?');
      row = stmt.get(id);
    }

    return row ? this.mapRowToDocument(row) : null;
  }

  static async findAll(limit?: number, userId?: number): Promise<DatabaseDocument[]> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let query;
    let stmt;
    let rows;

    if (userId) {
      query = limit
        ? 'SELECT * FROM documents WHERE (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC LIMIT ?'
        : 'SELECT * FROM documents WHERE (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC';
      stmt = db.prepare(query);
      rows = limit ? stmt.all(userId, limit) : stmt.all(userId);
    } else {
      query = limit
        ? 'SELECT * FROM documents ORDER BY created_at DESC LIMIT ?'
        : 'SELECT * FROM documents ORDER BY created_at DESC';
      stmt = db.prepare(query);
      rows = limit ? stmt.all(limit) : stmt.all();
    }

    return (rows as any[]).map(row => this.mapRowToDocument(row));
  }

  static async markAsRead(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE documents 
      SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async update(id: string, updateData: Partial<CreateDocumentData & { 
    isRead?: boolean; 
    status?: "pending" | "approved" | "rejected";
    approvedAt?: string;
    approvedBy?: string;
  }>): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const updateFields: string[] = [];
    const values: any[] = [];
    const now = new Date().toISOString();

    if (updateData.templateName !== undefined) {
      updateFields.push('template_name = ?');
      values.push(updateData.templateName);
    }

    if (updateData.applicantsName !== undefined) {
      updateFields.push('applicants_name = ?');
      values.push(updateData.applicantsName);
    }



    if (updateData.pdfFileName !== undefined) {
      updateFields.push('pdf_filename = ?');
      values.push(updateData.pdfFileName);
    }

    if (updateData.pdfData !== undefined) {
      updateFields.push('pdf_data = ?');
      values.push(updateData.pdfData ? JSON.stringify(updateData.pdfData) : null);
    }

    if (updateData.userId !== undefined) {
      updateFields.push('user_id = ?');
      values.push(updateData.userId);
    }

    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      values.push(updateData.status);
    }

    if (updateData.approvedAt !== undefined) {
      updateFields.push('approved_at = ?');
      values.push(updateData.approvedAt);
    }



    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    values.push(now);

    // Add the id for the WHERE clause
    values.push(id);

    const query = `UPDATE documents SET ${updateFields.join(', ')} WHERE id = ?`;
    const stmt = db.prepare(query);
    const result = stmt.run(...values);

    return result.changes > 0;
  }

  static async markAllAsRead(): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE documents
      SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE is_read = FALSE
    `);
    const result = stmt.run();

    return result.changes;
  }

  static async delete(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM documents WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async deleteAll(): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM documents');
    const result = stmt.run();
    return result.changes;
  }

  static async getUnreadCount(userId?: number): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let result;
    
    if (userId) {
      stmt = db.prepare(`
        SELECT COUNT(*) as count 
        FROM documents 
        WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE
      `);
      result = stmt.get(userId) as any;
    } else {
      stmt = db.prepare('SELECT COUNT(*) as count FROM documents WHERE is_read = FALSE');
      result = stmt.get() as any;
    }
    
    return result.count;
  }

  static async getByStatus(status: "pending" | "approved" | "rejected", userId?: number): Promise<DatabaseDocument[]> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let rows;

    if (userId) {
      stmt = db.prepare('SELECT * FROM documents WHERE status = ? AND (user_id = ? OR user_id IS NULL) ORDER BY created_at DESC');
      rows = stmt.all(status, userId);
    } else {
      stmt = db.prepare('SELECT * FROM documents WHERE status = ? ORDER BY created_at DESC');
      rows = stmt.all(status);
    }

    return (rows as any[]).map(row => this.mapRowToDocument(row));
  }

  private static mapRowToDocument(row: any): DatabaseDocument {
    return {
      id: row.id,
      templateName: row.template_name,
      applicantsName: row.applicants_name,
      uploadedAt: row.uploaded_at,
      pdfFileName: row.pdf_filename,
      pdfData: row.pdf_data ? JSON.parse(row.pdf_data) : undefined,
      userId: row.user_id,
      status: row.status,
      approvedAt: row.approved_at
    };
  }
}
