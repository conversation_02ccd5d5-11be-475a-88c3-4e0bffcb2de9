"use client";

import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface TruncatedTextProps {
  text: string;
  className?: string;
  maxLines?: number;
  seeMoreText?: string;
  seeLessText?: string;
}

export function TruncatedText({
  text,
  className,
  maxLines = 3,
  seeMoreText = "...see more",
  seeLessText = "see less",
}: TruncatedTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [shouldTruncate, setShouldTruncate] = useState(false);
  const textRef = useRef<HTMLParagraphElement>(null);
  const fullTextRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current && fullTextRef.current) {
        const fullTextHeight = fullTextRef.current.scrollHeight;

        // Calculate line height
        const computedStyle = window.getComputedStyle(textRef.current);
        const lineHeight = parseFloat(computedStyle.lineHeight);
        const maxHeight = lineHeight * maxLines;

        // Check if text overflows the max height
        setShouldTruncate(fullTextHeight > maxHeight);
      }
    };

    // Check truncation on mount and resize
    checkTruncation();
    window.addEventListener("resize", checkTruncation);

    return () => window.removeEventListener("resize", checkTruncation);
  }, [text, maxLines]);

  const toggleExpanded = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="relative">
      {/* Hidden element to measure full text height */}
      <p
        ref={fullTextRef}
        className={cn("absolute invisible pointer-events-none", className)}
        style={{ top: -9999 }}
      >
        {text}
      </p>

      {/* Visible text */}
      <p
        ref={textRef}
        className={cn(
          "text-sm text-muted-foreground leading-relaxed",
          className
        )}
        style={{
          display: "-webkit-box",
          WebkitLineClamp: isExpanded ? "unset" : maxLines,
          WebkitBoxOrient: "vertical",
          overflow: isExpanded ? "visible" : "hidden",
        }}
      >
        {text}
        {shouldTruncate && (
          <button
            onClick={toggleExpanded}
            className="ml-1 text-primary hover:text-primary/80 font-medium transition-colors inline"
          >
            {isExpanded ? seeLessText : seeMoreText}
          </button>
        )}
      </p>
    </div>
  );
}
