import { NextResponse } from 'next/server';
import { resetDatabase } from '@/lib/database';

// POST /api/debug/reset-database - Reset the entire database (DELETE ALL DATA)
export async function POST() {
  try {
    console.log('🚨 Database reset requested...');
    
    // Reset the database (this will delete all data and recreate tables)
    await resetDatabase();
    
    return NextResponse.json({
      success: true,
      message: 'Database reset successfully! All tables have been recreated empty.',
      warning: 'All data has been permanently deleted.'
    });
    
  } catch (error) {
    console.error('Error resetting database:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to reset database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/debug/reset-database - Get reset confirmation (safety check)
export async function GET() {
  return NextResponse.json({
    message: 'Database reset endpoint ready',
    warning: '⚠️ POST to this endpoint will DELETE ALL DATA permanently!',
    instructions: 'Send a POST request to reset the database',
    tables_that_will_be_reset: [
      'users',
      'templates', 
      'notifications',
      'archives',
      'qr_codes',
      'qr_validations',
      'migrations'
    ]
  });
}
