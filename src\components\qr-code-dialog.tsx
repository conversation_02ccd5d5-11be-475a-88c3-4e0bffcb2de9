"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { QrCode, Wifi, Download, Copy, Check, Link } from "lucide-react";
import { toast } from "sonner";
import QRCode from "qrcode";

interface QRCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QRCodeDialog({ open, onOpenChange }: QRCodeDialogProps) {
  const [ssid, setSsid] = useState("");
  const [password, setPassword] = useState("");
  const [wifiQrCodeDataUrl, setWifiQrCodeDataUrl] = useState<string>("");
  const [linkQrCodeDataUrl, setLinkQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [copiedWifi, setCopiedWifi] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setSsid("");
      setPassword("");
      setWifiQrCodeDataUrl("");
      setLinkQrCodeDataUrl("");
      setCopiedWifi(false);
      setCopiedLink(false);
    }
  }, [open]);

  const generateQRCodes = async () => {
    if (!ssid.trim()) {
      toast.error("Please enter a WiFi network name (SSID)");
      return;
    }

    setIsGenerating(true);
    try {
      // Get the network IP from the server to ensure mobile devices can access it
      const response = await fetch("/api/network-ip");
      const { baseURL, success } = await response.json();

      if (!success) {
        toast.error("Could not detect network IP address");
        return;
      }

      // Generate WiFi QR Code (standard WiFi format)
      const wifiString = `WIFI:T:WPA;S:${ssid};P:${password};H:false;;`;
      const wifiQrDataUrl = await QRCode.toDataURL(wifiString, {
        width: 250,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Generate Link QR Code (direct URL to upload page)
      const uploadUrl = `${baseURL}/notifications/upload`;
      const linkQrDataUrl = await QRCode.toDataURL(uploadUrl, {
        width: 250,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setWifiQrCodeDataUrl(wifiQrDataUrl);
      setLinkQrCodeDataUrl(linkQrDataUrl);
      toast.success("QR codes generated successfully!");
    } catch (error) {
      console.error("Error generating QR codes:", error);
      toast.error("Failed to generate QR codes");
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadWifiQRCode = () => {
    if (!wifiQrCodeDataUrl) return;

    const link = document.createElement("a");
    link.download = `wifi-qr-${ssid}.png`;
    link.href = wifiQrCodeDataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("WiFi QR code downloaded!");
  };

  const downloadLinkQRCode = () => {
    if (!linkQrCodeDataUrl) return;

    const link = document.createElement("a");
    link.download = `link-qr-${ssid}.png`;
    link.href = linkQrCodeDataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("Link QR code downloaded!");
  };

  const copyWifiToClipboard = async () => {
    if (!wifiQrCodeDataUrl) return;

    try {
      // Convert data URL to blob
      const response = await fetch(wifiQrCodeDataUrl);
      const blob = await response.blob();

      // Copy to clipboard
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob }),
      ]);

      setCopiedWifi(true);
      toast.success("WiFi QR code copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedWifi(false), 2000);
    } catch (error) {
      console.error("Error copying WiFi QR code:", error);
      toast.error("Failed to copy WiFi QR code");
    }
  };

  const copyLinkToClipboard = async () => {
    if (!linkQrCodeDataUrl) return;

    try {
      // Convert data URL to blob
      const response = await fetch(linkQrCodeDataUrl);
      const blob = await response.blob();

      // Copy to clipboard
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob }),
      ]);

      setCopiedLink(true);
      toast.success("Link QR code copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedLink(false), 2000);
    } catch (error) {
      console.error("Error copying Link QR code:", error);
      toast.error("Failed to copy Link QR code");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Generate QR Codes
          </DialogTitle>
          <DialogDescription>
            Create separate QR codes for WiFi connection and direct page access.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Hotspot Configuration */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                Hotspot Network Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label htmlFor="ssid">Network Name (SSID)</Label>
                <Input
                  id="ssid"
                  value={ssid}
                  onChange={(e) => setSsid(e.target.value)}
                  placeholder="Enter your WiFi network name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your WiFi password"
                  className="mt-1"
                />
              </div>
              <Button
                onClick={generateQRCodes}
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR Codes
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* WiFi QR Code Display */}
          {wifiQrCodeDataUrl && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Wifi className="h-4 w-4" />
                  WiFi Connection QR Code
                </CardTitle>
                <CardDescription className="text-xs">
                  Scan this code to automatically connect to the WiFi network.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-center">
                  <img
                    src={wifiQrCodeDataUrl}
                    alt="WiFi QR Code"
                    className="border rounded-lg"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={copyWifiToClipboard}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    {copiedWifi ? (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={downloadWifiQRCode}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Link QR Code Display */}
          {linkQrCodeDataUrl && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Link className="h-4 w-4" />
                  Upload Page QR Code
                </CardTitle>
                <CardDescription className="text-xs">
                  Scan this code to go directly to the upload page.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-center">
                  <img
                    src={linkQrCodeDataUrl}
                    alt="Upload Page QR Code"
                    className="border rounded-lg"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={copyLinkToClipboard}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    {copiedLink ? (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={downloadLinkQRCode}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
            <CardContent className="pt-4">
              <div className="space-y-2 text-sm">
                <p className="font-medium text-blue-800 dark:text-blue-200">
                  How to use:
                </p>
                <ol className="list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300">
                  <li>
                    Share your device's internet connection as a WiFi hotspot
                  </li>
                  <li>
                    Use the network name and password from your hotspot settings
                    above
                  </li>
                  <li>Generate both QR codes using the button above</li>
                  <li>
                    <strong>WiFi QR Code:</strong> Users scan this to
                    automatically connect to your WiFi network
                  </li>
                  <li>
                    <strong>Upload Page QR Code:</strong> Users scan this to go
                    directly to the upload page (after connecting to WiFi)
                  </li>
                </ol>
                <div className="mt-3 p-2 bg-green-100 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <p className="text-xs text-green-800 dark:text-green-200">
                    <strong>✓ New:</strong> Two separate QR codes provide better
                    user experience - one for WiFi connection and one for direct
                    page access!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
