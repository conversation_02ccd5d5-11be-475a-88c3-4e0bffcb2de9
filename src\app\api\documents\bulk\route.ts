import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel } from '@/lib/models/document';

// PATCH /api/documents/bulk - Bulk operations (mark all as read, mark selected as read, approve/reject multiple)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, documentIds, status, approvedBy } = body;

    if (action === 'markAllAsRead') {
      const updatedCount = await DocumentModel.markAllAsRead();

      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} documents marked as read`
      });
    }

    if (action === 'markSelectedAsRead' && documentIds && Array.isArray(documentIds)) {
      let updatedCount = 0;
      for (const id of documentIds) {
        try {
          await DocumentModel.markAsRead(id);
          updatedCount++;
        } catch (error) {
          console.error(`Error marking document ${id} as read:`, error);
        }
      }

      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} documents marked as read`
      });
    }

    if (action === 'updateStatus' && documentIds && Array.isArray(documentIds) && status) {
      let updatedCount = 0;
      const updateData: any = { status };
      
      if (status === 'approved') {
        updateData.approvedAt = new Date().toISOString();
        updateData.approvedBy = approvedBy || 'admin';
      }

      for (const id of documentIds) {
        try {
          await DocumentModel.update(id, updateData);
          updatedCount++;
        } catch (error) {
          console.error(`Error updating document ${id} status:`, error);
        }
      }

      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} documents updated to ${status}`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid bulk action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

// DELETE /api/documents/bulk - Bulk delete operations
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { documentIds } = body;

    if (!documentIds || !Array.isArray(documentIds)) {
      return NextResponse.json(
        { success: false, error: 'documentIds array is required' },
        { status: 400 }
      );
    }

    let deletedCount = 0;
    for (const id of documentIds) {
      try {
        await DocumentModel.delete(id);
        deletedCount++;
      } catch (error) {
        console.error(`Error deleting document ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `${deletedCount} documents deleted successfully`
    });
  } catch (error) {
    console.error('Error performing bulk delete:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk delete' },
      { status: 500 }
    );
  }
}
