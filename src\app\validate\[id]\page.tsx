"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, FileText, Calendar, User, MapPin } from "lucide-react";
import { format } from "date-fns";

interface ValidatedDocument {
  id: string;
  template_name: string;
  applicant_name: string;
  barangay?: string;
  approved_at: string;
  approved_by?: string;
  ctc_number?: string;
  or_number?: string;
}

export default function ValidatePage() {
  const params = useParams();
  const [document, setDocument] = useState<ValidatedDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const validateDocument = async () => {
      if (!params.id) return;

      try {
        const response = await fetch(`/api/validate/${params.id}`);
        const data = await response.json();

        if (data.success) {
          setDocument(data.document);
        } else {
          setError(data.error || "Document not found or invalid");
        }
      } catch (err) {
        console.error("Validation error:", err);
        setError("Failed to validate document");
      } finally {
        setLoading(false);
      }
    };

    validateDocument();
  }, [params.id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-lg">Validating document...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-xl">
        <CardHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            {document ? (
              <div className="rounded-full bg-green-100 dark:bg-green-900/20 p-3">
                <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />
              </div>
            ) : (
              <div className="rounded-full bg-red-100 dark:bg-red-900/20 p-3">
                {error?.includes("not found") ? (
                  <XCircle className="h-12 w-12 text-red-600 dark:text-red-400" />
                ) : (
                  <AlertCircle className="h-12 w-12 text-red-600 dark:text-red-400" />
                )}
              </div>
            )}
          </div>
          
          <CardTitle className="text-2xl font-bold">
            {document ? "Document Validated" : "Validation Failed"}
          </CardTitle>
          
          {document ? (
            <p className="text-muted-foreground mt-2">
              This document is valid and has been officially approved.
            </p>
          ) : (
            <p className="text-muted-foreground mt-2">
              {error || "This document could not be validated."}
            </p>
          )}
        </CardHeader>

        {document && (
          <CardContent className="space-y-6">
            {/* Document Status */}
            <div className="flex justify-center">
              <Badge variant="default" className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 text-sm">
                <CheckCircle className="w-4 h-4 mr-2" />
                APPROVED & VALID
              </Badge>
            </div>

            {/* Document Details */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <User className="h-4 w-4" />
                  <span>Applicant</span>
                </div>
                <p className="font-medium">{document.applicant_name}</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <FileText className="h-4 w-4" />
                  <span>Document Type</span>
                </div>
                <p className="font-medium">{document.template_name}</p>
              </div>

              {document.barangay && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>Barangay</span>
                  </div>
                  <p className="font-medium">{document.barangay}</p>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Approved Date</span>
                </div>
                <p className="font-medium">
                  {format(new Date(document.approved_at), "MMMM dd, yyyy 'at' hh:mm a")}
                </p>
              </div>
            </div>

            {/* Additional Information */}
            {(document.ctc_number || document.or_number || document.approved_by) && (
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-3">Additional Information</h3>
                <div className="grid gap-3 text-sm">
                  {document.ctc_number && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">CTC Number:</span>
                      <span className="font-medium">{document.ctc_number}</span>
                    </div>
                  )}
                  {document.or_number && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">OR Number:</span>
                      <span className="font-medium">{document.or_number}</span>
                    </div>
                  )}
                  {document.approved_by && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Approved By:</span>
                      <span className="font-medium">{document.approved_by}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Verification Notice */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Verification Complete
                  </p>
                  <p className="text-blue-700 dark:text-blue-300">
                    This document has been verified against our official records and is confirmed to be authentic and valid.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
