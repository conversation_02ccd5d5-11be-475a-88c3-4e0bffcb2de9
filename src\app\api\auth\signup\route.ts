import { NextRequest, NextResponse } from 'next/server';
import { signup } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Initialize database if not already done
    await initDatabase();
    
    const body = await request.json();
    const { username, password, recoveryMethod, privateKey, securityQuestions } = body;

    if (!username || !password || !recoveryMethod) {
      return NextResponse.json(
        { success: false, message: 'Username, password, and recovery method are required' },
        { status: 400 }
      );
    }

    if (recoveryMethod === 'securityQuestions' && (!securityQuestions || securityQuestions.length === 0)) {
      return NextResponse.json(
        { success: false, message: 'Security questions are required when using security questions recovery method' },
        { status: 400 }
      );
    }

    const result = await signup({
      username,
      password,
      recoveryMethod,
      privateKey,
      securityQuestions
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        privateKey: result.privateKey // Only returned if using private key method
      });
    } else {
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Signup API error:', error);
    return NextResponse.json(
      { success: false, message: 'Signup failed' },
      { status: 500 }
    );
  }
}
