import { NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

// GET /api/archives/debug - Debug archived documents
export async function GET() {
  try {
    const database = await getDatabase();
    
    // Get all archived documents
    const archivedDocs = database.prepare(`
      SELECT id, first_name, last_name, approved_at, template_id
      FROM archives 
      ORDER BY approved_at DESC 
      LIMIT 10
    `).all();
    
    // Count total archives
    const totalCount = database.prepare('SELECT COUNT(*) as count FROM archives').get() as { count: number };

    return NextResponse.json({
      success: true,
      summary: {
        totalArchives: totalCount.count,
        recentArchives: archivedDocs.length
      },
      recentArchives: archivedDocs
    });
  } catch (error) {
    console.error('Error debugging archives:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to debug archives',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
