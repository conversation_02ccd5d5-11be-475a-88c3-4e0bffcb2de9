"use client";

import { useState, useEffect } from "react";
import { <PERSON>, FileText, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/contexts/notification-context";
import { useDeleteNotificationMutation } from "@/hooks/useNotifications";
import { formatDistanceToNow } from "date-fns";
import { useRouter } from "next/navigation";

export function NotificationDropdown() {
  const router = useRouter();
  const [adminMode, setAdminMode] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const {
    notifications,
    unreadCount,
    mark<PERSON><PERSON><PERSON>,
    markAll<PERSON>Read,
    clearAllNotifications,
  } = useNotifications();

  // Use the delete mutation directly to use mutateAsync
  const deleteNotificationMutation = useDeleteNotificationMutation();

  // Check admin mode on component mount and listen for changes
  useEffect(() => {
    // Set hydrated to true on client side
    setIsHydrated(true);

    const checkAdminMode = () => {
      const savedAdminMode = localStorage.getItem("adminMode") === "true";
      setAdminMode(savedAdminMode);
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    window.addEventListener("adminModeChanged", handleAdminModeChange);
    return () => {
      window.removeEventListener("adminModeChanged", handleAdminModeChange);
    };
  }, []);

  // Don't render anything during SSR or if not in admin mode after hydration
  if (!isHydrated || !adminMode) {
    return null;
  }

  const handleNotificationClick = (notification: {
    id: string;
    isRead: boolean;
    documentId?: string;
  }) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    // Navigate to document details page if documentId exists
    if (notification.documentId) {
      router.push(`/documents/${notification.documentId}`);
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    // Prevent double-clicking
    if (deletingIds.has(notificationId)) {
      return;
    }

    setDeletingIds((prev) => new Set(prev).add(notificationId));

    try {
      await deleteNotificationMutation.mutateAsync(notificationId);
    } finally {
      setDeletingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <div className="flex items-center justify-between px-3 py-2">
          <DropdownMenuLabel className="p-0">Notifications</DropdownMenuLabel>
          {notifications.length > 0 && (
            <div className="flex gap-1">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="h-6 px-2 text-xs"
                >
                  Mark all read
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllNotifications}
                className="h-6 px-2 text-xs text-destructive hover:text-destructive"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>

        <DropdownMenuSeparator />

        {notifications.length === 0 ? (
          <div className="px-3 py-8 text-center text-sm text-muted-foreground">
            No notifications yet
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto">
            {notifications.slice(0, 10).map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`flex flex-col items-start gap-2 p-3 cursor-pointer ${
                  !notification.isRead ? "bg-muted/50" : ""
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start gap-2 w-full">
                  <FileText className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between gap-2">
                      <p className="font-medium text-sm truncate">
                        {notification.templateName}
                      </p>
                      {!notification.isRead && (
                        <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0" />
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Applicant: {notification.applicantsName}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatDistanceToNow(notification.uploadedAt, {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNotification(notification.id);
                    }}
                    disabled={deletingIds.has(notification.id)}
                    className="h-6 w-6 p-0 opacity-50 hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground disabled:opacity-25"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </DropdownMenuItem>
            ))}

            {notifications.length > 10 && (
              <div className="px-3 py-2 text-center text-xs text-muted-foreground border-t">
                Showing 10 of {notifications.length} notifications
              </div>
            )}
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
