@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-poppins);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --font-poppins: "Poppins", system-ui, sans-serif;
  /* Modern Light Theme with #55a4ff as primary */
  --background: #fefefe;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #55a4ff; /* Primary blue color */
  --primary-foreground: #ffffff;
  --secondary: #f4f4f5;
  --secondary-foreground: #1a1a1a;
  --muted: #f4f4f5;
  --muted-foreground: #6b7280;
  --accent: #f1f5f9;
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e4e4e7;
  --input: #f4f4f5;
  --ring: #55a4ff;
  --chart-1: #55a4ff; /* Primary blue */
  --chart-2: #06b6d4; /* Complementary teal */
  --chart-3: #55a4ff; /* Main color #55a4ff */
  --chart-4: #f59e0b; /* Warm accent */
  --chart-5: #8b5cf6; /* Purple accent */
  --sidebar: #ffffff;
  --sidebar-foreground: #1a1a1a;
  --sidebar-primary: #55a4ff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #1a1a1a;
  --sidebar-border: #e4e4e7;
  --sidebar-ring: #55a4ff;
}

.dark {
  /* Modern Dark Theme with #55a4ff as primary */
  --background: #01060c;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #60a5fa; /* Slightly brighter #55a4ff for dark mode */
  --primary-foreground: #0f172a;
  --secondary: #334155;
  --secondary-foreground: #f1f5f9;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #475569;
  --accent-foreground: #f1f5f9;
  --destructive: #f87171;
  --destructive-foreground: #f1f5f9;
  --border: #475569;
  --input: #334155;
  --ring: #60a5fa;
  --chart-1: #60a5fa; /* Primary blue */
  --chart-2: #22d3ee; /* Complementary teal */
  --chart-3: #60a5fa; /* Main color #55a4ff */
  --chart-4: #fbbf24; /* Warm accent */
  --chart-5: #a78bfa; /* Purple accent */
  --sidebar: #1e293b;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #60a5fa;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #475569;
  --sidebar-accent-foreground: #f1f5f9;
  --sidebar-border: #475569;
  --sidebar-ring: #60a5fa;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins);
  }
}
