import { NextResponse } from "next/server";
import { UserModel } from "@/lib/models/user";

export async function GET() {
  try {
    const users = await UserModel.getAllUsers();
    
    return NextResponse.json({
      success: true,
      hasUsers: users.length > 0,
      userCount: users.length
    });
  } catch (error) {
    console.error("Error checking users:", error);
    return NextResponse.json(
      { success: false, message: "Failed to check users" },
      { status: 500 }
    );
  }
}
