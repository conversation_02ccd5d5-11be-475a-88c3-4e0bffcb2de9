import { NextRequest } from 'next/server';
import { UserModel } from '@/lib/models/user';

/**
 * Get the current admin user from the session
 */
export async function getCurrentAdmin(request?: NextRequest): Promise<string | null> {
  try {
    // If we have a request object (server-side), check the session cookie
    if (request) {
      const sessionCookie = request.cookies.get('admin-session');
      
      if (!sessionCookie) {
        return null;
      }

      // Decode the session token
      const sessionData = Buffer.from(sessionCookie.value, 'base64').toString();
      const [username] = sessionData.split(':');

      // Verify the user exists
      const user = await UserModel.findByUsername(username);
      if (user) {
        return username;
      }
    }

    // For client-side or when no request is available, return a generic admin ID
    // In a real application, you'd want to implement proper session management
    return 'admin';
  } catch (error) {
    console.error('Error getting current admin:', error);
    return null;
  }
}

/**
 * Get admin ID for client-side operations
 * This is a simplified version for client-side use
 */
export function getClientAdminId(): string {
  // In a real application, you'd get this from a proper auth context
  // For now, return a generic admin ID
  return 'admin';
}
