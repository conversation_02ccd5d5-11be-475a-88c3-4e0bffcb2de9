import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';

// PATCH /api/notifications/bulk - Bulk operations (mark all as read, mark selected as read)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, notificationIds } = body;

    if (action === 'markAllAsRead') {
      const updatedCount = await NotificationModel.markAllAsRead();

      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} notifications marked as read`
      });
    }

    if (action === 'markSelectedAsRead' && notificationIds && Array.isArray(notificationIds)) {
      let updatedCount = 0;
      for (const id of notificationIds) {
        try {
          await NotificationModel.markAsRead(id);
          updatedCount++;
        } catch (error) {
          console.error(`Error marking notification ${id} as read:`, error);
        }
      }

      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} notifications marked as read`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid bulk action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/bulk - Bulk delete operations
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { notificationIds } = body;

    if (!notificationIds || !Array.isArray(notificationIds)) {
      return NextResponse.json(
        { success: false, error: 'notificationIds array is required' },
        { status: 400 }
      );
    }

    let deletedCount = 0;
    for (const id of notificationIds) {
      try {
        await NotificationModel.delete(id);
        deletedCount++;
      } catch (error) {
        console.error(`Error deleting notification ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `${deletedCount} notifications deleted successfully`
    });
  } catch (error) {
    console.error('Error performing bulk delete:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk delete' },
      { status: 500 }
    );
  }
}
