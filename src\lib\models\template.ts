import { getDatabase } from '../database';
import { Template } from '@/types/template';

// Database row interface for templates
interface TemplateRow {
  id: string;
  name?: string; // Old column name
  template_name?: string; // New column name
  description: string;
  filename: string;
  placeholders: string;
  layout_size: string;
  uploaded_at: string;
  user_id?: number;
  has_applicant_photo?: number; // SQLite boolean stored as integer
}

export class TemplateModel {
  static async findById(id: string, userId?: number): Promise<Template | null> {
    const db = await getDatabase();
    let stmt;
    let row: TemplateRow | undefined;

    // Check if user_id column exists
    const tableInfo = db.prepare("PRAGMA table_info(templates)").all() as any[];
    const hasUserId = tableInfo.some((col: any) => col.name === 'user_id');

    if (userId && hasUserId) {
      stmt = db.prepare('SELECT * FROM templates WHERE id = ? AND (user_id = ? OR user_id IS NULL)');
      row = stmt.get(id, userId) as TemplateRow | undefined;
    } else {
      stmt = db.prepare('SELECT * FROM templates WHERE id = ?');
      row = stmt.get(id) as TemplateRow | undefined;
    }
    
    if (!row) return null;
    
    return {
      id: row.id,
      name: row.template_name || row.name || '', // Support both old and new column names during migration
      description: row.description,
      filename: row.filename,
      placeholders: JSON.parse(row.placeholders || '[]'),
      layoutSize: row.layout_size as 'A4' | 'Letter',
      hasApplicantPhoto: Boolean(row.has_applicant_photo),
      uploadedAt: row.uploaded_at,
    };
  }

  static async findAll(userId?: number): Promise<Template[]> {
    const db = await getDatabase();
    let stmt;
    let rows: TemplateRow[];

    console.log('TemplateModel.findAll called with userId:', userId);

    // Check if user_id column exists
    const tableInfo = db.prepare("PRAGMA table_info(templates)").all() as any[];
    const hasUserId = tableInfo.some((col: any) => col.name === 'user_id');

    if (userId && hasUserId) {
      stmt = db.prepare('SELECT * FROM templates WHERE user_id = ? OR user_id IS NULL ORDER BY uploaded_at DESC');
      rows = stmt.all(userId) as TemplateRow[];
      console.log('Query with userId - found rows:', rows.length);
    } else {
      stmt = db.prepare('SELECT * FROM templates ORDER BY uploaded_at DESC');
      rows = stmt.all() as TemplateRow[];
      console.log('Query without userId - found rows:', rows.length);
    }

    console.log('Raw rows from database:', rows);
    
    return rows.map(row => ({
      id: row.id,
      name: row.template_name || row.name || '',
      description: row.description,
      filename: row.filename,
      placeholders: JSON.parse(row.placeholders || '[]'),
      layoutSize: row.layout_size as 'A4' | 'Letter',
      hasApplicantPhoto: Boolean(row.has_applicant_photo),
      uploadedAt: row.uploaded_at,
    }));
  }

  static async create(template: Omit<Template, 'uploadedAt'>, userId?: number): Promise<Template> {
    const db = await getDatabase();

    // Check which columns exist
    const tableInfo = db.prepare("PRAGMA table_info(templates)").all() as any[];
    const hasTemplateType = tableInfo.some((col: any) => col.name === 'template_type');
    const hasUserId = tableInfo.some((col: any) => col.name === 'user_id');

    let stmt;
    let values;

    if (hasTemplateType && hasUserId) {
      // Include both template_type and user_id columns
      stmt = db.prepare(`
        INSERT INTO templates (id, name, description, filename, template_type, placeholders, layout_size, has_applicant_photo, user_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      values = [
        template.id,
        template.name,
        template.description,
        template.filename,
        'general', // default template type
        JSON.stringify(template.placeholders),
        template.layoutSize,
        template.hasApplicantPhoto ? 1 : 0,
        userId || null
      ];
    } else if (hasTemplateType && !hasUserId) {
      // Include template_type but not user_id
      stmt = db.prepare(`
        INSERT INTO templates (id, name, description, filename, template_type, placeholders, layout_size, has_applicant_photo)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      values = [
        template.id,
        template.name,
        template.description,
        template.filename,
        'general', // default template type
        JSON.stringify(template.placeholders),
        template.layoutSize,
        template.hasApplicantPhoto ? 1 : 0
      ];
    } else if (!hasTemplateType && hasUserId) {
      // Include user_id but not template_type
      stmt = db.prepare(`
        INSERT INTO templates (id, name, description, filename, placeholders, layout_size, has_applicant_photo, user_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);
      values = [
        template.id,
        template.name,
        template.description,
        template.filename,
        JSON.stringify(template.placeholders),
        template.layoutSize,
        template.hasApplicantPhoto ? 1 : 0,
        userId || null
      ];
    } else {
      // Neither template_type nor user_id columns exist
      stmt = db.prepare(`
        INSERT INTO templates (id, name, description, filename, placeholders, layout_size, has_applicant_photo)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      values = [
        template.id,
        template.name,
        template.description,
        template.filename,
        JSON.stringify(template.placeholders),
        template.layoutSize,
        template.hasApplicantPhoto ? 1 : 0
      ];
    }

    const uploadedAt = new Date().toISOString();
    stmt.run(...values);
    
    return {
      ...template,
      uploadedAt
    };
  }

  static async update(id: string, updates: Partial<Omit<Template, 'id' | 'uploadedAt'>>, userId?: number): Promise<boolean> {
    const db = await getDatabase();

    const fields = [];
    const values = [];

    if (updates.name !== undefined) {
      fields.push('name = ?');
      values.push(updates.name);
    }

    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }

    if (updates.filename !== undefined) {
      fields.push('filename = ?');
      values.push(updates.filename);
    }



    if (updates.placeholders !== undefined) {
      fields.push('placeholders = ?');
      values.push(JSON.stringify(updates.placeholders));
    }

    if (updates.layoutSize !== undefined) {
      fields.push('layout_size = ?');
      values.push(updates.layoutSize);
    }

    if (updates.hasApplicantPhoto !== undefined) {
      fields.push('has_applicant_photo = ?');
      values.push(updates.hasApplicantPhoto ? 1 : 0);
    }

    if (fields.length === 0) return false;

    fields.push('updated_at = CURRENT_TIMESTAMP');

    let whereClause = 'WHERE id = ?';
    values.push(id);

    if (userId) {
      whereClause += ' AND (user_id = ? OR user_id IS NULL)';
      values.push(userId);
    }

    const stmt = db.prepare(`
      UPDATE templates
      SET ${fields.join(', ')}
      ${whereClause}
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  }

  static async delete(id: string, userId?: number): Promise<boolean> {
    const db = await getDatabase();

    let whereClause = 'WHERE id = ?';
    const values: any[] = [id];

    if (userId) {
      whereClause += ' AND (user_id = ? OR user_id IS NULL)';
      values.push(userId);
    }

    const stmt = db.prepare(`DELETE FROM templates ${whereClause}`);
    const result = stmt.run(...values);
    return result.changes > 0;
  }

  static async search(query: string, userId?: number): Promise<Template[]> {
    const db = await getDatabase();
    let stmt;
    let rows;

    const searchTerm = `%${query}%`;

    if (userId) {
      stmt = db.prepare(`
        SELECT * FROM templates
        WHERE (name LIKE ? OR description LIKE ?) AND (user_id = ? OR user_id IS NULL)
        ORDER BY uploaded_at DESC
      `);
      rows = stmt.all(searchTerm, searchTerm, userId) as any[];
    } else {
      stmt = db.prepare(`
        SELECT * FROM templates
        WHERE name LIKE ? OR description LIKE ?
        ORDER BY uploaded_at DESC
      `);
      rows = stmt.all(searchTerm, searchTerm) as any[];
    }
    
    return rows.map(row => ({
      id: row.id,
      name: row.template_name || row.name || '',
      description: row.description,
      filename: row.filename,
      placeholders: JSON.parse(row.placeholders || '[]'),
      layoutSize: row.layout_size as 'A4' | 'Letter',
      hasApplicantPhoto: Boolean(row.has_applicant_photo),
      uploadedAt: row.uploaded_at,
    }));
  }

  static async getByLayoutSize(layoutSize: 'A4' | 'Letter'): Promise<Template[]> {
    const db = await getDatabase();
    const stmt = db.prepare('SELECT * FROM templates WHERE layout_size = ? ORDER BY uploaded_at DESC');
    const rows = stmt.all(layoutSize) as any[];
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      filename: row.filename,
      placeholders: JSON.parse(row.placeholders || '[]'),
      layoutSize: row.layout_size as 'A4' | 'Letter',
      hasApplicantPhoto: Boolean(row.has_applicant_photo),
      uploadedAt: row.uploaded_at,
    }));
  }



  static async count(): Promise<number> {
    const db = await getDatabase();
    const stmt = db.prepare('SELECT COUNT(*) as count FROM templates');
    const result = stmt.get() as { count: number };
    return result.count;
  }
}

// Note: Removed unused DocumentModel and DocumentRecord interface
// The documents table was never actually used in the application
