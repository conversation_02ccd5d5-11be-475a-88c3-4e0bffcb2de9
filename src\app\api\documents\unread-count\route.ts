import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel } from '@/lib/models/document';

// GET /api/documents/unread-count - Get unread document count
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    const unreadCount = await DocumentModel.getUnreadCount(
      userId ? parseInt(userId) : undefined
    );
    
    return NextResponse.json({
      success: true,
      unreadCount
    });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch unread count' },
      { status: 500 }
    );
  }
}
