import { NextResponse } from 'next/server';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

export async function GET() {
  try {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return NextResponse.json({
        success: false,
        error: 'Supabase not configured',
        message: 'NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables are required',
        envCheck: {
          hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        }
      }, { status: 500 });
    }

    // Test Supabase connection
    const { error } = await supabase
      .from('qr_codes')
      .select('*', { count: 'exact' })
      .limit(1);

    if (error) {
      return NextResponse.json({
        success: false,
        error: 'Supabase connection failed',
        details: error.message,
        hint: error.hint || 'Check if qr_codes table exists in Supabase'
      });
    }

    // Try to get existing QR codes
    const { data: qrCodes, error: selectError } = await supabase
      .from('qr_codes')
      .select('*')
      .limit(10);

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful',
      qrCodesCount: qrCodes?.length || 0,
      qrCodes: qrCodes || [],
      selectError: selectError?.message || null
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to connect to Supabase',
      details: error instanceof Error ? error.message : 'Unknown error',
      envCheck: {
        hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        url: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...'
      }
    }, { status: 500 });
  }
}
