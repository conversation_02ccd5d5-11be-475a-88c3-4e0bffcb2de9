import { NextRequest } from 'next/server';
import os from 'os';

/**
 * Get the actual network IP address of the server
 * This detects the real network interface IP, not just from headers
 */
function getServerNetworkIP(): string {
  const interfaces = os.networkInterfaces();

  // Look for the first non-internal IPv4 address
  for (const name of Object.keys(interfaces)) {
    const iface = interfaces[name];
    if (!iface) continue;

    for (const alias of iface) {
      if (alias.family === 'IPv4' && !alias.internal) {
        return alias.address;
      }
    }
  }

  // Fallback to localhost if no network interface found
  return 'localhost';
}

/**
 * Get the network IP address from the request headers or server network interfaces
 * This will automatically detect the correct IP address for the current network
 */
export function getNetworkIP(request: NextRequest): string {
  // First, try to get the actual server network IP
  const serverIP = getServerNetworkIP();
  if (serverIP !== 'localhost') {
    return `http://${serverIP}:3000`;
  }

  // Try to get the host from the request headers
  const host = request.headers.get('host');

  if (host) {
    // Extract IP from host header (format: "***********:3000" or "localhost:3000")
    const [ip] = host.split(':');

    // If it's not localhost, use the detected IP
    if (ip !== 'localhost' && ip !== '127.0.0.1') {
      return `http://${host}`;
    }
  }

  // Try to get from x-forwarded-host header (common in reverse proxies)
  const forwardedHost = request.headers.get('x-forwarded-host');
  if (forwardedHost) {
    return `http://${forwardedHost}`;
  }

  // Try to get from x-forwarded-for header
  const forwardedFor = request.headers.get('x-forwarded-for');
  if (forwardedFor) {
    const ip = forwardedFor.split(',')[0].trim();
    return `http://${ip}:3000`;
  }

  // Check for other common headers
  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return `http://${realIP}:3000`;
  }

  // Fallback to localhost if we can't detect the network IP
  return 'http://localhost:3000';
}

/**
 * Get the base URL for the current request
 * This includes protocol, host, and port
 */
export function getBaseURL(request: NextRequest): string {
  return getNetworkIP(request);
}

/**
 * Client-side function to get the current base URL
 * This runs in the browser and detects the current page's URL
 */
export function getClientBaseURL(): string {
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }
  return 'http://localhost:3000';
}
