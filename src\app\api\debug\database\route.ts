import { NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

export async function GET() {
  try {
    const db = await getDatabase();
    
    // Check if templates table exists and get its schema
    const templatesTableInfo = db.prepare("PRAGMA table_info(templates)").all();
    
    // Get count of templates
    const templatesCount = db.prepare('SELECT COUNT(*) as count FROM templates').get() as { count: number };
    
    // Get all templates
    const templates = db.prepare('SELECT * FROM templates LIMIT 5').all();
    
    // Check if users table exists
    let usersTableInfo = [];
    let usersCount = 0;
    try {
      usersTableInfo = db.prepare("PRAGMA table_info(users)").all();
      usersCount = (db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number }).count;
    } catch (error) {
      console.log('Users table might not exist:', error);
    }
    
    // Check migrations table
    let migrationsInfo = [];
    try {
      migrationsInfo = db.prepare('SELECT * FROM migrations').all();
    } catch (error) {
      console.log('Migrations table might not exist:', error);
    }
    
    return NextResponse.json({
      success: true,
      database: {
        templatesTable: {
          exists: templatesTableInfo.length > 0,
          columns: templatesTableInfo,
          count: templatesCount.count,
          sampleData: templates
        },
        usersTable: {
          exists: usersTableInfo.length > 0,
          columns: usersTableInfo,
          count: usersCount
        },
        migrations: migrationsInfo,
        dbPath: process.env.NODE_ENV === 'development' ? 'data/ldis.db' : 'Database path hidden in production'
      }
    });
  } catch (error) {
    console.error('Database debug error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to debug database',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
