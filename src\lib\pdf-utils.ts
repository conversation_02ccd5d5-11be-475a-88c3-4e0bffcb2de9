// PDF utility functions for client-side PDF processing

// Dynamic import function to load PDF.js only on client side
async function loadPdfJs() {
  if (typeof window === 'undefined') {
    throw new Error('PDF.js can only be used on the client side');
  }

  const pdfjsLib = await import('pdfjs-dist');

  // Try to use CDN worker, fallback to disabling worker if it fails
  try {
    pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.3.93/pdf.worker.min.mjs`;
  } catch (error) {
    console.warn('Failed to set PDF.js worker, using main thread:', error);
    // Create a minimal inline worker as fallback
    const workerBlob = new Blob(['// Minimal worker'], { type: 'application/javascript' });
    pdfjsLib.GlobalWorkerOptions.workerSrc = URL.createObjectURL(workerBlob);
  }

  return pdfjsLib;
}

/**
 * Convert a PDF file to an image (first page)
 * @param file - The PDF file to convert
 * @param scale - Scale factor for rendering (default: 2.0, optimized for mobile)
 * @returns Promise that resolves to a data URL of the rendered image
 */
// Helper function to add timeout to promises
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

export async function pdfToImage(file: File, scale: number = 2.0): Promise<string> {
  try {
    // Load PDF.js dynamically (client-side only)
    const pdfjsLib = await loadPdfJs();

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document with timeout to prevent hanging
    const pdf = await withTimeout(
      pdfjsLib.getDocument({
        data: arrayBuffer,
        useWorkerFetch: false,
        isEvalSupported: false,
        useSystemFonts: true,
        disableAutoFetch: false,
        disableStream: false,
        stopAtErrors: true
      }).promise,
      10000 // 10 second timeout
    );
    
    // Get the first page with timeout
    const page = await withTimeout(pdf.getPage(1), 5000);

    // Create a canvas element
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    if (!context) {
      throw new Error('Could not get canvas context');
    }

    // Calculate the viewport
    const viewport = page.getViewport({ scale });

    // Set canvas dimensions
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    // Render the page with timeout
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    await withTimeout(page.render(renderContext).promise, 10000);
    
    // Convert canvas to data URL with optimized quality for mobile
    return canvas.toDataURL('image/jpeg', 0.85); // Reduced from 0.95 to 0.85 for better performance
    
  } catch (error) {
    console.error('Error converting PDF to image:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('DOMMatrix')) {
        throw new Error('PDF.js requires a browser environment with DOM support');
      } else if (error.message.includes('Invalid PDF') || error.message.includes('corrupted')) {
        throw new Error('The PDF file appears to be corrupted or invalid.');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('Network error while loading PDF. Please check your connection.');
      } else if (error.message.includes('canvas')) {
        throw new Error('Canvas rendering failed. Your browser may not support this PDF.');
      }
    }

    throw new Error(`Failed to convert PDF to image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert multiple PDF pages to images
 * @param file - The PDF file to convert
 * @param maxPages - Maximum number of pages to convert (default: 1)
 * @param scale - Scale factor for rendering (default: 2.0, optimized for mobile)
 * @returns Promise that resolves to an array of data URLs
 */
export async function pdfToImages(file: File, maxPages: number = 1, scale: number = 2.0): Promise<string[]> {
  try {
    // Load PDF.js dynamically (client-side only)
    const pdfjsLib = await loadPdfJs();

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const numPages = Math.min(pdf.numPages, maxPages);
    const images: string[] = [];
    
    // Convert each page to image
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Could not get canvas context');
      }
      
      // Calculate the viewport
      const viewport = page.getViewport({ scale });
      
      // Set canvas dimensions
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      
      await page.render(renderContext).promise;
      
      // Convert canvas to data URL with optimized quality and add to array
      images.push(canvas.toDataURL('image/jpeg', 0.85)); // Optimized for mobile performance
    }
    
    return images;
    
  } catch (error) {
    console.error('Error converting PDF pages to images:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('DOMMatrix')) {
        throw new Error('PDF.js requires a browser environment with DOM support');
      } else if (error.message.includes('worker')) {
        throw new Error('PDF worker failed to load. Please check your internet connection.');
      }
    }

    throw new Error(`Failed to convert PDF pages to images: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
