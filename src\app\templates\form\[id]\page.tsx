"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  FileText,
  Download,
  Camera,
  User,
  Calendar,
  Hash,
  MapPin,
} from "lucide-react";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  getApplicantPhotoDimensions,
  processImageToFit,
  groupPlaceholders,
} from "@/lib/template-utils";
import { capitalizeWords } from "@/lib/utils";

interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: "A4" | "Letter";
  uploadedAt: string;
  hasApplicantPhoto?: boolean;
}

export default function TemplateFormPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [template, setTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [applicantPhoto, setApplicantPhoto] = useState<File | null>(null);
  const [isProcessingPhoto, setIsProcessingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoDimensions, setPhotoDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [originalDocumentId, setOriginalDocumentId] = useState<string | null>(
    null
  );
  const [originalNotificationId, setOriginalNotificationId] = useState<
    string | null
  >(null);

  const loadTemplate = useCallback(
    async (id: string) => {
      try {
        const response = await fetch("/api/templates");
        if (!response.ok) {
          throw new Error("Failed to load templates");
        }
        const templates = await response.json();
        const foundTemplate = templates.find((t: Template) => t.id === id);

        if (!foundTemplate) {
          toast.error("Template not found");
          router.push("/");
          return;
        }

        setTemplate(foundTemplate);

        // Only initialize form data if not in edit mode
        // Edit mode will populate data from notification
        const isEdit = searchParams.get("edit") === "true";
        if (!isEdit) {
          // Initialize form data with empty values for all placeholders
          const initialData: Record<string, string> = {};
          foundTemplate.placeholders.forEach((placeholder: string) => {
            const key = placeholder.replace(/[\[\]]/g, ""); // Remove brackets
            initialData[key] = "";
          });
          setFormData(initialData);
        }

        // If template has applicant photo, load the template content to get dimensions
        if (foundTemplate.hasApplicantPhoto) {
          try {
            const templateResponse = await fetch(
              `/api/templates/${foundTemplate.id}`
            );
            if (templateResponse.ok) {
              const templateData = await templateResponse.json();
              const dimensions = getApplicantPhotoDimensions(
                templateData.content
              );
              setPhotoDimensions(dimensions);
            }
          } catch (error) {
            console.error("Error loading template content:", error);
            // Use default dimensions if we can't load template content
            setPhotoDimensions({ width: 150, height: 200 });
          }
        }
      } catch (error) {
        console.error("Error loading template:", error);
        toast.error("Failed to load template");
        router.push("/");
      } finally {
        setIsLoading(false);
      }
    },
    [router, searchParams]
  );

  useEffect(() => {
    if (params.id) {
      loadTemplate(params.id as string);
    }
  }, [params.id, loadTemplate]);

  // Handle URL parameters for edit mode
  useEffect(() => {
    const isEdit = searchParams.get("edit") === "true";
    const documentId = searchParams.get("documentId");
    const notificationId = searchParams.get("notificationId"); // Keep for backward compatibility

    // Only proceed if we have a template loaded and we're in edit mode
    if (isEdit && (documentId || notificationId) && template) {
      setIsEditMode(true);

      if (documentId) {
        setOriginalDocumentId(documentId);
      } else if (notificationId) {
        setOriginalNotificationId(notificationId);
      }

      // Helper function to populate form data
      const populateFormData = async (pdfData: any) => {
        // Initialize form data with template placeholders first
        const initialData: Record<string, string> = {};
        template.placeholders.forEach((placeholder: string) => {
          const key = placeholder.replace(/[\[\]]/g, ""); // Remove brackets
          initialData[key] = "";
        });

        // Then merge with existing PDF data
        if (pdfData.userData) {
          Object.assign(initialData, pdfData.userData);
        }

        setFormData(initialData);

        // Pre-fill photo if available
        if (pdfData.photoBase64) {
          setPhotoPreview(pdfData.photoBase64);

          // Convert base64 to File object for the applicantPhoto state
          try {
            const response = await fetch(pdfData.photoBase64);
            const blob = await response.blob();
            const file = new File([blob], "applicant-photo.jpg", {
              type: "image/jpeg",
            });
            setApplicantPhoto(file);
          } catch (error) {
            console.error("Error converting base64 to file:", error);
          }
        }
      };

      // Fetch the document/notification data to get PDF data including photo
      const fetchDocumentData = async () => {
        try {
          let response, data;

          if (documentId) {
            // Try to fetch as document first
            response = await fetch(`/api/documents/${documentId}`);
            data = await response.json();

            if (data.success && data.document.pdfData) {
              const pdfData = data.document.pdfData;
              await populateFormData(pdfData);
            }
          } else if (notificationId) {
            // Fall back to notification API
            response = await fetch(`/api/notifications/${notificationId}`);
            data = await response.json();

            if (data.success && data.notification.documentId) {
              // Fetch the associated document to get PDF data
              const docResponse = await fetch(
                `/api/documents/${data.notification.documentId}`
              );
              const docData = await docResponse.json();

              if (docData.success && docData.document.pdfData) {
                const pdfData = docData.document.pdfData;
                await populateFormData(pdfData);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching document data:", error);
          toast.error("Failed to load existing data");
        }
      };

      fetchDocumentData();
    }
  }, [searchParams, template]);

  const isNumericField = (key: string) => {
    const k = key.toLowerCase();
    return k.includes("age") || k.includes("ctc") || k.includes("number");
  };

  const handleInputChange = (key: string, value: string) => {
    const newValue = isNumericField(key) ? value.replace(/\D/g, "") : value;
    setFormData((prev) => ({ ...prev, [key]: newValue }));
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      if (!validTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPEG, PNG, or GIF)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast.error("Image file size must be less than 5MB");
        return;
      }

      // Create preview URL for the original file
      const previewUrl = URL.createObjectURL(file);
      setPhotoPreview(previewUrl);

      // Automatically process the image to fit template dimensions
      if (photoDimensions) {
        setIsProcessingPhoto(true);
        try {
          const processedFile = await processImageToFit(
            file,
            photoDimensions.width,
            photoDimensions.height
          );
          setApplicantPhoto(processedFile);
          toast.success("Photo processed and fitted automatically");
        } catch (error) {
          console.error("Error processing photo:", error);
          toast.error("Failed to process photo. Using original.");
          setApplicantPhoto(file);
        } finally {
          setIsProcessingPhoto(false);
        }
      } else {
        // If no dimensions available, use the file as-is
        setApplicantPhoto(file);
        toast.success("Photo selected successfully");
      }
    }
  };

  // Helper function to convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  // Helper function to extract full name from form data
  const getFullNameFromFormData = (data: Record<string, string>): string => {
    // Look for common name field patterns
    const nameFields = Object.keys(data).filter((key) => {
      const lowerKey = key.toLowerCase();
      return (
        lowerKey.includes("name") ||
        lowerKey.includes("applicant") ||
        lowerKey.includes("initial") ||
        lowerKey.includes("suffix")
      );
    });

    // Try to find full name field first
    const fullNameField = nameFields.find((key) => {
      const lowerKey = key.toLowerCase();
      return (
        lowerKey.includes("full") ||
        lowerKey.includes("complete") ||
        (lowerKey.includes("name") &&
          !lowerKey.includes("first") &&
          !lowerKey.includes("last") &&
          !lowerKey.includes("middle"))
      );
    });

    if (fullNameField && data[fullNameField]) {
      return data[fullNameField].trim();
    }

    // If no full name field, try to combine first, middle, last, suffix names
    const firstNameField = nameFields.find((key) =>
      key.toLowerCase().includes("first")
    );
    const middleNameField = nameFields.find((key) => {
      const lowerKey = key.toLowerCase();
      return lowerKey.includes("middle") || lowerKey.includes("initial");
    });
    const lastNameField = nameFields.find(
      (key) =>
        key.toLowerCase().includes("last") ||
        key.toLowerCase().includes("surname")
    );
    const suffixField = nameFields.find((key) =>
      key.toLowerCase().includes("suffix")
    );

    const nameParts = [];
    if (firstNameField && data[firstNameField]) {
      nameParts.push(data[firstNameField].trim());
    }

    if (middleNameField && data[middleNameField]) {
      const middleName = data[middleNameField].trim();
      // Intelligent middle initial formatting for filename
      if (middleName.length > 0) {
        if (middleName.endsWith(".")) {
          nameParts.push(middleName.charAt(0).toUpperCase() + ".");
        } else {
          nameParts.push(middleName.charAt(0).toUpperCase() + ".");
        }
      }
    }

    if (lastNameField && data[lastNameField]) {
      nameParts.push(data[lastNameField].trim());
    }

    if (suffixField && data[suffixField]) {
      const suffix = data[suffixField].trim().toLowerCase();
      // Intelligent suffix formatting for filename
      if (suffix === "jr" || suffix === "jr.") {
        nameParts.push("Jr.");
      } else if (suffix === "sr" || suffix === "sr.") {
        nameParts.push("Sr.");
      } else if (suffix === "iii" || suffix === "3rd") {
        nameParts.push("III");
      } else if (suffix === "ii" || suffix === "2nd") {
        nameParts.push("II");
      } else if (suffix === "iv" || suffix === "4th") {
        nameParts.push("IV");
      } else if (suffix === "v" || suffix === "5th") {
        nameParts.push("V");
      } else {
        nameParts.push(data[suffixField].trim());
      }
    }

    if (nameParts.length > 0) {
      return nameParts.join(" ");
    }

    // Fallback: use the first name field found
    if (nameFields.length > 0 && data[nameFields[0]]) {
      return data[nameFields[0]].trim();
    }

    return "Unknown";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    // Validate that all non-date fields are filled
    const emptyFields = Object.entries(formData).filter(([key, value]) => {
      const lowerKey = key.toLowerCase();
      // Skip validation for date fields
      const isDateField =
        lowerKey.includes("date") ||
        lowerKey.includes("day") ||
        lowerKey.includes("month") ||
        lowerKey.includes("year");
      // Skip validation for optional fields (middle initial and suffix)
      const isOptionalField =
        lowerKey.includes("initial") ||
        lowerKey.includes("suffix") ||
        (lowerKey.includes("middle") && lowerKey.includes("initial"));
      return !isDateField && !isOptionalField && !value.trim();
    });
    if (emptyFields.length > 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate applicant photo if required (skip validation in edit mode if photo preview exists)
    if (template.hasApplicantPhoto && !applicantPhoto && !photoPreview) {
      toast.error("Please upload an applicant photo");
      return;
    }

    setIsGenerating(true);

    try {
      // Upload applicant photo if provided
      let photoPath = null;
      let useExistingPhoto = false;

      console.log(
        "Photo processing - applicantPhoto:",
        !!applicantPhoto,
        "photoPreview:",
        !!photoPreview,
        "isEditMode:",
        isEditMode
      );

      if (template.hasApplicantPhoto) {
        if (applicantPhoto) {
          console.log("Uploading new photo");
          // New photo uploaded
          const photoFormData = new FormData();
          photoFormData.append("templateId", template.id);
          photoFormData.append("photo", applicantPhoto);

          const photoResponse = await fetch("/api/templates/upload-photo", {
            method: "POST",
            body: photoFormData,
          });

          if (!photoResponse.ok) {
            const error = await photoResponse.json();
            throw new Error(error.error || "Failed to upload photo");
          }

          const photoResult = await photoResponse.json();
          photoPath = photoResult.photoPath;
        } else if (photoPreview && isEditMode) {
          console.log("Using existing photo from preview");
          // Use existing photo in edit mode - will handle this after getting HTML
          useExistingPhoto = true;
        }
      }

      // Prepare form data, ensuring date fields are empty and values capitalized
      const processedFormData = { ...formData };
      Object.keys(processedFormData).forEach((key) => {
        const lower = key.toLowerCase();
        const isDateField =
          lower.includes("date") ||
          lower.includes("day") ||
          lower.includes("month") ||
          lower.includes("year");
        const isMiddleInitialField =
          lower.includes("initial") ||
          (lower.includes("middle") && lower.includes("initial"));
        const isSuffixField = lower.includes("suffix");

        if (isDateField) {
          processedFormData[key] = "";
        } else if (isMiddleInitialField) {
          // Intelligent middle initial formatting
          const value = processedFormData[key].trim();
          if (value.length > 0) {
            // If already has a period, don't add another
            if (value.endsWith(".")) {
              processedFormData[key] = value.charAt(0).toUpperCase() + ".";
            } else {
              // Always convert to single letter with period
              processedFormData[key] = value.charAt(0).toUpperCase() + ".";
            }
          }
        } else if (isSuffixField) {
          // Intelligent suffix formatting
          const value = processedFormData[key].trim().toLowerCase();
          if (value.length > 0) {
            // Handle common suffix patterns
            if (value === "jr" || value === "jr.") {
              processedFormData[key] = "Jr.";
            } else if (value === "sr" || value === "sr.") {
              processedFormData[key] = "Sr.";
            } else if (value === "iii" || value === "3rd") {
              processedFormData[key] = "III";
            } else if (value === "ii" || value === "2nd") {
              processedFormData[key] = "II";
            } else if (value === "iv" || value === "4th") {
              processedFormData[key] = "IV";
            } else if (value === "v" || value === "5th") {
              processedFormData[key] = "V";
            } else {
              // For other suffixes, capitalize properly
              processedFormData[key] = capitalizeWords(processedFormData[key]);
            }
          }
        } else {
          processedFormData[key] = capitalizeWords(processedFormData[key]);
        }
      });

      // Generate the document by calling the API
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          data: processedFormData,
          photoPath: photoPath,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate document");
      }

      const result = await response.json();

      // Create an iframe to render the HTML content in complete isolation
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-10000px";
      iframe.style.top = "-10000px";
      iframe.style.width = "794px"; // A4 width at 96dpi
      iframe.style.height = "1123px"; // A4 height at 96dpi
      iframe.style.border = "none";
      iframe.style.visibility = "hidden";
      document.body.appendChild(iframe);

      // Wait for iframe to be ready
      await new Promise((resolve) => {
        iframe.onload = resolve;
        iframe.src = "about:blank";
      });

      // Write the HTML content to the iframe
      const iframeDoc =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error("Could not access iframe document");
      }

      iframeDoc.open();
      iframeDoc.write(result.htmlContent);
      iframeDoc.close();

      // Handle existing photo replacement if needed
      if (useExistingPhoto && photoPreview) {
        console.log("Replacing existing photo in template");
        const photoElements = iframeDoc.querySelectorAll(
          'img[data-placeholder="applicant-photo"], img[src*="applicant-photo"]'
        );
        console.log("Found photo elements:", photoElements.length);
        photoElements.forEach((img: any) => {
          console.log("Replacing photo src for element:", img);
          img.src = photoPreview;
        });
      }

      // Wait for content to render
      await new Promise((resolve) => setTimeout(resolve, 1000));

      try {
        // Convert the iframe content to canvas with optimized settings
        const canvas = await html2canvas(iframeDoc.body, {
          scale: 1, // Reduced from 2 to 1 for smaller file size
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
          width: 794,
          height: 1123,
          scrollX: 0,
          scrollY: 0,
          windowWidth: 794,
          windowHeight: 1123,
        });

        // Create PDF with JPEG compression for smaller file size
        const imgData = canvas.toDataURL("image/jpeg", 0.85); // JPEG with 90% quality
        const pdf = new jsPDF({
          orientation: "portrait",
          unit: "mm",
          format: result.layoutSize === "A4" ? "a4" : "letter",
        });

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        pdf.addImage(imgData, "JPEG", 0, 0, pdfWidth, pdfHeight);

        // Add embedded data as invisible text in the PDF
        let photoBase64 = undefined;
        if (applicantPhoto) {
          // New photo uploaded
          photoBase64 = await fileToBase64(applicantPhoto);
        } else if (photoPreview && isEditMode) {
          // Use existing photo in edit mode
          photoBase64 = photoPreview;
        }

        const embeddedData = {
          templateId: template.id,
          templateName: template.name,
          placeholders: template.placeholders,
          userData: processedFormData,
          photoBase64: photoBase64,
          generatedAt: new Date().toISOString(),
          layoutSize: result.layoutSize,
          status: null, // Initial status - null means pending/not reviewed
        };

        // Add the embedded data as invisible text
        pdf.setTextColor(255, 255, 255); // White text (invisible on white background)
        pdf.setFontSize(1); // Very small font
        pdf.text(
          `LDIS_DATA_BEGIN:${JSON.stringify(embeddedData)}:LDIS_DATA_END`,
          1,
          1
        );

        // Download the PDF with format: Title Of Document (Full Name Of Applicant)
        const fullName = getFullNameFromFormData(processedFormData);

        // Helper function to capitalize each word and add spaces
        const formatForFilename = (text: string): string => {
          return text
            .replace(/[^a-z0-9\s]/gi, "") // Remove special characters but keep spaces
            .split(/\s+/) // Split by whitespace
            .filter((word) => word.length > 0) // Remove empty strings
            .map(
              (word) =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            ) // Capitalize each word
            .join(" "); // Join with spaces
        };

        const formattedTemplateName = formatForFilename(result.templateName);
        const formattedFullName = formatForFilename(fullName);
        const filename = `${formattedTemplateName} (${formattedFullName}).pdf`;

        if (isEditMode && (originalDocumentId || originalNotificationId)) {
          const editId = originalDocumentId || originalNotificationId;
          const isDocument = !!originalDocumentId;

          console.log(
            `Edit mode detected, updating ${
              isDocument ? "document" : "notification"
            }:`,
            editId
          );

          // Get the current document/notification to access old file path
          const currentResponse = await fetch(
            isDocument
              ? `/api/documents/${editId}`
              : `/api/notifications/${editId}`
          );
          const currentData = await currentResponse.json();
          const oldFilePath = isDocument
            ? currentData.document?.pdfFilePath
            : currentData.notification?.pdfFilePath;

          // In edit mode, directly update the document without creating a new one
          console.log("Edit mode: Updating existing document directly");

          // Convert PDF to base64 for storage
          const pdfBlob = pdf.output("blob");
          const pdfArrayBuffer = await pdfBlob.arrayBuffer();
          const pdfBase64 = btoa(
            String.fromCharCode(...new Uint8Array(pdfArrayBuffer))
          );

          console.log("Generated PDF data for update:", filename);

          // Update the original document with the new PDF data
          const updatePayload = {
            templateName: filename,
            message: `Document updated at ${new Date().toLocaleTimeString()}`,
            pdfFileName: filename,
            pdfData: {
              ...embeddedData,
              pdfBase64: pdfBase64, // Include the PDF data directly
              templateId: template.id,
              templateName: template.name,
              updatedAt: new Date().toISOString(),
            },
          };

          console.log(
            `Updating ${
              isDocument ? "document" : "notification"
            } with payload:`,
            updatePayload
          );

          const updateResponse = await fetch(
            isDocument
              ? `/api/documents/${editId}`
              : `/api/notifications/${editId}`,
            {
              method: "PATCH",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(updatePayload),
            }
          );

          console.log("Update response status:", updateResponse.status);

          if (!updateResponse.ok) {
            const errorData = await updateResponse.json();
            console.error("Update failed:", errorData);
            throw new Error(
              `Failed to update ${isDocument ? "document" : "notification"}`
            );
          }

          const updateResult = await updateResponse.json();
          console.log("Update result:", updateResult);

          if (updateResult.success) {
            toast.success("Document updated successfully!");
          } else {
            throw new Error(updateResult.error || "Failed to update document");
          }

          // Add a small delay to ensure the user sees the success message
          // and the backend has time to process the update
          setTimeout(() => {
            console.log(
              `Redirecting to updated ${
                isDocument ? "document" : "notification"
              }:`,
              editId
            );
            router.push(`/documents/${editId}`);
          }, 1000);
        } else {
          // Normal mode - just download the PDF
          pdf.save(filename);
          toast.success(
            `PDF document generated and downloaded successfully! (${result.layoutSize} format)`
          );
        }

        // Clean up uploaded photo after successful generation
        if (template.hasApplicantPhoto && applicantPhoto) {
          try {
            await fetch("/api/templates/delete-photo", {
              method: "DELETE",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                templateId: template.id,
              }),
            });
            console.log("Uploaded photo cleaned up successfully");
          } catch (error) {
            console.error("Error cleaning up photo:", error);
            // Don't show error to user as document was generated successfully
          }
        }
      } finally {
        // Clean up the iframe
        document.body.removeChild(iframe);
      }
    } catch (error) {
      console.error("Error generating document:", error);
      toast.error("Failed to generate document");
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!template) {
    return null;
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-full sm:max-w-2xl">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-foreground mb-2 break-words">
          {template.name}
        </h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          {template.description}
        </p>
      </div>

      <div className="bg-card text-card-foreground rounded-lg shadow-xl border overflow-hidden">
        <div className="p-3 sm:p-6">
          <div className="flex items-center gap-2 mb-4 sm:mb-6">
            <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-primary flex-shrink-0" />
            <h2 className="text-lg sm:text-xl font-semibold break-words">
              {isEditMode
                ? "Edit Document Information"
                : "Document Information"}
            </h2>
          </div>
          <p className="text-muted-foreground mb-6 sm:mb-8 text-sm sm:text-base">
            {isEditMode
              ? "Update the information below to modify the document."
              : "Fill in the required information to generate your document."}
          </p>
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Photo Upload Section - Moved to top */}
            {template.hasApplicantPhoto && (
              <div className="flex flex-col items-center mb-6 sm:mb-8">
                <div className="relative">
                  <input
                    id="applicant-photo"
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    className="hidden"
                    disabled={isProcessingPhoto}
                  />

                  {photoPreview ? (
                    <div className="relative">
                      <img
                        src={photoPreview}
                        alt="Photo preview"
                        className="w-32 h-32 sm:w-40 sm:h-40 object-cover rounded-lg border-2 border-border shadow-lg"
                      />
                      {isProcessingPhoto && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                          <div className="text-white text-xs sm:text-sm font-medium">
                            Processing...
                          </div>
                        </div>
                      )}
                      <label
                        htmlFor="applicant-photo"
                        className="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 bg-primary hover:bg-primary/90 text-primary-foreground text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded cursor-pointer transition-colors"
                      >
                        Change
                      </label>
                    </div>
                  ) : (
                    <label
                      htmlFor="applicant-photo"
                      className={`w-32 h-32 sm:w-40 sm:h-40 rounded-lg border-2 border-dashed border-border flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition-colors ${
                        isProcessingPhoto ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                    >
                      <Camera className="h-8 w-8 sm:h-10 sm:w-10 text-muted-foreground mb-2 sm:mb-3" />
                      <span className="text-xs sm:text-sm text-muted-foreground text-center px-2">
                        Upload Photo
                      </span>
                    </label>
                  )}
                </div>
              </div>
            )}

            {/* Form Fields - Grouped by Category */}
            {Object.entries(groupPlaceholders(template.placeholders)).map(
              ([groupName, placeholders]) => {
                // Get icon for each group
                const getGroupIcon = (name: string) => {
                  switch (name) {
                    case "Personal Information":
                      return <User className="h-4 w-4" />;
                    case "Date Information":
                      return <Calendar className="h-4 w-4" />;
                    case "Document Information":
                      return <Hash className="h-4 w-4" />;
                    case "Contact Information":
                    case "Other Information":
                      return <MapPin className="h-4 w-4" />;
                    default:
                      return <FileText className="h-4 w-4" />;
                  }
                };

                return (
                  <div
                    key={groupName}
                    className="space-y-3 sm:space-y-4 mb-6 sm:mb-8"
                  >
                    <div className="flex items-center gap-2 pb-2 sm:pb-3 border-b">
                      <span className="text-primary flex-shrink-0">
                        {getGroupIcon(groupName)}
                      </span>
                      <h3 className="text-base sm:text-lg font-semibold break-words">
                        {groupName}
                      </h3>
                    </div>
                    <div className="grid grid-cols-1 gap-3 sm:gap-4">
                      {placeholders.map((placeholder) => {
                        const key = placeholder.replace(/[\[\]]/g, ""); // Remove brackets for form key
                        const label =
                          key.charAt(0).toUpperCase() + key.slice(1); // Capitalize first letter

                        // Check if this is a date field or optional field
                        const lowerKey = key.toLowerCase();
                        const isDateField =
                          lowerKey.includes("date") ||
                          lowerKey.includes("day") ||
                          lowerKey.includes("month") ||
                          lowerKey.includes("year");
                        const isOptionalField =
                          lowerKey.includes("initial") ||
                          lowerKey.includes("suffix") ||
                          (lowerKey.includes("middle") &&
                            lowerKey.includes("initial"));
                        const numericField =
                          lowerKey.includes("age") ||
                          lowerKey.includes("ctc") ||
                          lowerKey.includes("number");

                        return (
                          <div
                            key={placeholder}
                            className="space-y-1.5 sm:space-y-2"
                          >
                            <Label
                              htmlFor={key}
                              className="text-xs sm:text-sm font-medium break-words"
                            >
                              {label}
                            </Label>
                            <Input
                              id={key}
                              type={numericField ? "number" : "text"}
                              inputMode={numericField ? "numeric" : undefined}
                              pattern={numericField ? "\\d*" : undefined}
                              placeholder={
                                isDateField
                                  ? "Will be filled automatically"
                                  : isOptionalField
                                  ? `Enter ${label.toLowerCase()} (optional)`
                                  : `Enter ${label.toLowerCase()}`
                              }
                              value={formData[key] || ""}
                              onChange={(e) =>
                                handleInputChange(key, e.target.value)
                              }
                              required={!isDateField && !isOptionalField}
                              disabled={isDateField}
                              className={`text-sm sm:text-base ${
                                isDateField
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                              }`}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4 sm:pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isGenerating}
                className="flex-1 text-sm sm:text-base"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isGenerating}
                className="flex-1 text-sm sm:text-base"
              >
                {isGenerating ? (
                  isEditMode ? (
                    "Updating..."
                  ) : (
                    "Generating..."
                  )
                ) : (
                  <>
                    <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                    {isEditMode ? "Update Document" : "Generate Document"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
