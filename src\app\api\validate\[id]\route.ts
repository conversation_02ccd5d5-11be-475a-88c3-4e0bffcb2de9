import { NextRequest, NextResponse } from 'next/server';
import { getArchivedDocument, getDocumentQRCode } from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get the archived document from the database
    const document = await getArchivedDocument(id);

    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found or has not been approved',
          message: 'This document ID does not exist in our approved records.'
        },
        { status: 404 }
      );
    }

    // Check if QR code exists for this document
    const qrCode = await getDocumentQRCode(id);
    if (!qrCode) {
      return NextResponse.json(
        {
          success: false,
          error: 'QR code not found for this document',
          message: 'This document does not have a valid QR code.'
        },
        { status: 404 }
      );
    }

    // Parse metadata to get additional information
    let additionalInfo = {};
    if (document.metadata) {
      try {
        const metadata = JSON.parse(document.metadata);
        if (metadata.pdfData && metadata.pdfData.userData) {
          const userData = metadata.pdfData.userData;
          additionalInfo = {
            barangay: userData.BARANGAY || userData.barangay,
            or_number: userData['O.R. NUMBER'] || userData.or_number,
            ctc_number: userData.CTC_NUMBER || userData.ctc_number,
          };
        }
      } catch (error) {
        console.error('Error parsing document metadata:', error);
      }
    }

    // Return only the necessary information for validation
    const validationData = {
      id: document.id,
      template_name: document.template_name,
      applicant_name: document.applicant_name,
      approved_at: document.approved_at,
      approved_by: document.approved_by,
      ...additionalInfo
    };

    return NextResponse.json({
      success: true,
      document: validationData,
      message: 'Document is valid and approved'
    });

  } catch (error) {
    console.error('Document validation API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Validation service temporarily unavailable',
        message: 'Please try again later or contact support.'
      },
      { status: 500 }
    );
  }
}
