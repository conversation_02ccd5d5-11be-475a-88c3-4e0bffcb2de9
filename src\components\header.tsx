"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";
import { NotificationDropdown } from "@/components/notification-dropdown";
import { SidebarTrigger } from "@/components/ui/sidebar";

export function Header() {
  const { theme, setTheme } = useTheme();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [adminMode, setAdminMode] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Update isDarkMode when theme changes
  useEffect(() => {
    setIsDarkMode(theme === "dark");
  }, [theme]);

  // Check admin mode on component mount and listen for changes
  useEffect(() => {
    setIsHydrated(true);

    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, []);

  const handleThemeToggle = () => {
    const newTheme = isDarkMode ? "light" : "dark";
    setIsDarkMode(!isDarkMode);
    setTheme(newTheme);
  };

  return (
    <header className="sticky top-0 z-50 border-b bg-background p-4">
      <div className="flex items-center justify-between">
        {/* Sidebar Trigger and Logo */}
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <Link
            href={isHydrated && adminMode ? "/admin" : "/"}
            className="flex items-center space-x-2"
          >
            <Image
              src="/images/LDIS.png"
              alt="LDIS"
              width={28}
              height={28}
              className="h-7 w-7"
            />
            <h1 className="text-xl font-extrabold">LDIS</h1>
          </Link>
        </div>

        {/* Right side buttons */}
        <div className="flex items-center space-x-2">
          {/* Notification Dropdown */}
          <NotificationDropdown />

          {/* Theme Toggle Button */}
          <Button variant="outline" size="icon" onClick={handleThemeToggle}>
            {isDarkMode ? (
              <Moon className="h-4 w-4" />
            ) : (
              <Sun className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </header>
  );
}
