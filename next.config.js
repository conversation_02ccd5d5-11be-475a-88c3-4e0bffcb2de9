/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Exclude better-sqlite3 and pdfjs-dist from client-side bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };

      config.externals = config.externals || [];
      config.externals.push("better-sqlite3");
    } else {
      // Exclude pdfjs-dist from server-side bundle to prevent SSR issues
      config.externals = config.externals || [];
      config.externals.push("pdfjs-dist");
    }

    return config;
  },
  serverExternalPackages: ["better-sqlite3", "pdfjs-dist"],
};

module.exports = nextConfig;
