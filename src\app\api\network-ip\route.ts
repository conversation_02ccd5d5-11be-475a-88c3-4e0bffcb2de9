import { NextRequest, NextResponse } from 'next/server';
import { getNetworkIP } from '@/lib/network-utils';

export async function GET(request: NextRequest) {
  try {
    // Use the proper network IP detection
    const baseURL = getNetworkIP(request);
    const networkIP = baseURL.replace('http://', '');

    // Return the network IP as JSON
    return NextResponse.json({
      baseURL,
      networkIP,
      success: true
    });
  } catch (error) {
    console.error('Error getting network IP:', error);

    // Fallback to localhost
    return NextResponse.json({
      baseURL: 'http://localhost:3000',
      networkIP: 'localhost:3000',
      success: false,
      error: 'Could not detect network IP'
    });
  }
}
