import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';
import { DocumentModel } from '@/lib/models/document';
import fs from 'fs';
import path from 'path';

// GET /api/notifications/[id]/pdf - Generate PDF from JSON metadata
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notification = await NotificationModel.findById(id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Get PDF data from the associated document
    if (!notification.documentId) {
      return NextResponse.json(
        { success: false, error: 'No document associated with this notification' },
        { status: 404 }
      );
    }

    const document = await DocumentModel.findById(notification.documentId);
    if (!document || !document.pdfData) {
      return NextResponse.json(
        { success: false, error: 'Associated document not found or has no data' },
        { status: 404 }
      );
    }

    // Parse the document's PDF data
    let pdfData;
    try {
      pdfData = typeof document.pdfData === 'string'
        ? JSON.parse(document.pdfData)
        : document.pdfData;
    } catch (parseError) {
      console.error('Error parsing document PDF data:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid document data format' },
        { status: 400 }
      );
    }

    // Since we now work with JSON metadata instead of PDF files,
    // we return the metadata for the client to generate the preview
    return NextResponse.json({
      success: true,
      message: 'PDF files are no longer stored. Use the document preview feature instead.',
      pdfData: pdfData,
      suggestion: 'Use the DocumentPreview component to generate a live preview from the metadata.'
    });
  } catch (error) {
    console.error('Error serving PDF file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to serve PDF file' },
      { status: 500 }
    );
  }
}

// OPTIONS /api/notifications/[id]/pdf - Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

// HEAD /api/notifications/[id]/pdf - Handle HEAD requests for document data validation
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const notification = await NotificationModel.findById(id);

    // Check if notification has associated document with PDF data
    if (!notification || !notification.documentId) {
      return new NextResponse(null, { status: 404 });
    }

    const document = await DocumentModel.findById(notification.documentId);
    if (!document || !document.pdfData) {
      return new NextResponse(null, { status: 404 });
    }

    // Return headers indicating JSON metadata is available
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Error handling HEAD request for document data:', error);
    return new NextResponse(null, { status: 500 });
  }
}
