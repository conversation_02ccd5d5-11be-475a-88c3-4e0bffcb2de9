import { NextRequest } from 'next/server';
import { UserModel } from './models/user';

export interface SessionUser {
  username: string;
  id: number;
}

/**
 * Extract user information from session cookie
 * @param request - Next.js request object
 * @returns User information if session is valid, null otherwise
 */
export async function getUserFromSession(request: NextRequest): Promise<SessionUser | null> {
  try {
    const sessionCookie = request.cookies.get('admin-session');
    
    if (!sessionCookie) {
      return null;
    }

    // Decode the session token
    const decoded = Buffer.from(sessionCookie.value, 'base64').toString();
    let sessionData;
    
    try {
      // Try to parse as JSON (new format)
      sessionData = JSON.parse(decoded);
    } catch {
      // Fallback to old format for backward compatibility
      const [username, timestamp] = decoded.split(':');
      sessionData = {
        username,
        timestamp: parseInt(timestamp)
      };
    }
    
    // Check if session is expired (24 hours)
    const now = Date.now();
    const sessionAge = now - sessionData.timestamp;
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (sessionAge > maxAge) {
      return null;
    }

    // Verify user still exists
    const user = await UserModel.findByUsername(sessionData.username);
    if (!user) {
      return null;
    }

    return {
      username: user.username,
      id: user.id!
    };
  } catch (error) {
    console.error('Error extracting user from session:', error);
    return null;
  }
}

/**
 * Require authentication for API routes
 * @param request - Next.js request object
 * @returns User information if authenticated, throws error otherwise
 */
export async function requireAuth(request: NextRequest): Promise<SessionUser> {
  const user = await getUserFromSession(request);
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}
