"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>ertCircle, Loader2, ZoomIn, ZoomOut, RotateCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { pdfToImage } from "@/lib/pdf-utils";

interface PDFImageViewerProps {
  pdfUrl: string;
  fileName?: string;
  className?: string;
}

export function PDFImageViewer({
  pdfUrl,
  fileName,
  className,
}: PDFImageViewerProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState(1.5);
  const [rotation, setRotation] = useState(0);
  const [retryCount, setRetryCount] = useState(0);

  const loadPdfAsImage = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch the PDF file with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(pdfUrl, {
        signal: controller.signal,
        cache: "force-cache", // Cache PDF files for better performance
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch PDF: ${response.status} ${response.statusText}`
        );
      }

      const blob = await response.blob();
      const file = new File([blob], fileName || "document.pdf", {
        type: "application/pdf",
      });

      // Convert PDF to image using our utility function
      const imageDataUrl = await pdfToImage(file, scale);
      setImageUrl(imageDataUrl);
    } catch (err) {
      if (err instanceof Error && err.name === "AbortError") {
        setError("PDF loading timed out. Please try again.");
      } else {
        console.error("Error loading PDF as image:", err);
        setError(err instanceof Error ? err.message : "Failed to load PDF");
      }
    } finally {
      setIsLoading(false);
    }
  }, [pdfUrl, fileName, scale]);

  useEffect(() => {
    loadPdfAsImage();
  }, [loadPdfAsImage, retryCount]);

  const handleZoomIn = useCallback(() => {
    setScale((prev) => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale((prev) => Math.max(prev - 0.25, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360);
  }, []);

  const handleRetry = useCallback(() => {
    setRetryCount((prev) => prev + 1);
  }, []);

  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center h-full bg-background ${className}`}
      >
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-sm text-muted-foreground">
            Converting PDF to image...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex items-center justify-center h-full bg-background ${className}`}
      >
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2 text-destructive">
            PDF Loading Error
          </h3>
          <p className="text-muted-foreground text-sm mb-4">{error}</p>
          <div className="text-xs text-muted-foreground bg-muted p-3 rounded mb-4">
            <p>
              <strong>PDF URL:</strong> {pdfUrl}
            </p>
            {fileName && (
              <p>
                <strong>File Name:</strong> {fileName}
              </p>
            )}
          </div>
          <Button variant="outline" size="sm" onClick={handleRetry}>
            Retry Loading PDF
          </Button>
        </div>
      </div>
    );
  }

  if (!imageUrl) {
    return (
      <div
        className={`flex items-center justify-center h-full bg-background ${className}`}
      >
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No PDF Image Available</h3>
          <p className="text-muted-foreground">
            Failed to convert PDF to image.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-background ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomOut}
            disabled={scale <= 0.5}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium min-w-[60px] text-center">
            {Math.round(scale * 100)}%
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomIn}
            disabled={scale >= 3}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleRotate}>
            <RotateCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* PDF Image Display */}
      <div className="flex-1 overflow-auto p-4">
        <div className="flex justify-center">
          <img
            src={imageUrl}
            alt={fileName || "PDF Preview"}
            className="max-w-full h-auto shadow-lg rounded border"
            style={{
              transform: `rotate(${rotation}deg)`,
              transformOrigin: "center",
            }}
          />
        </div>
      </div>
    </div>
  );
}
